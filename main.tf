provider "aws" {
  region = var.region
}

terraform {
  required_providers {
    aws = {
      source = "hashicorp/aws"
      version = "5.67.0"
    }
  }
  required_version = ">= 1.5.1"
  backend "s3" {}
}

module "sns" {
  source          = "./modules/sns"
  region          = var.region
  accountId       = var.accountId
  environment_tag = var.environment_tag
}

module "iam" {
  environment_tag                  = var.environment_tag
  source                           = "./modules/iam"
  region                           = var.region
  accountId                        = var.accountId
  environment                      = var.environment
  lambda_role_arn                  = var.lambda_role_arn
  s3_cross_account_role_arn        = var.s3_cross_account_role_arn
  prod_oap_lambda_assumed_role_arn = var.prod_oap_lambda_assumed_role_arn
  dev_oap_lambda_assumed_role_arn  = var.dev_oap_lambda_assumed_role_arn
  dev_eip_integration_lambda_arn   = var.dev_eip_integration_lambda_arn
  prod_eip_integration_lambda_arn  = var.prod_eip_integration_lambda_arn
  dev_oap_lambda_notification_arn  = var.dev_oap_lambda_notification_arn
  prod_oap_lambda_notification_arn = var.prod_oap_lambda_notification_arn
  prod_user_access_arn             = var.prod_user_access_arn
  dev_user_access_arn              = var.dev_user_access_arn
}

module "s3" {
  environment_tag = var.environment_tag
  source          = "./modules/s3"
  region          = var.region
  accountId       = var.accountId
  environment     = var.environment
}
module "codebuild" {
  environment_tag                                              = var.environment_tag
  source                                                       = "./modules/codebuild"
  region                                                       = var.region
  accountId                                                    = var.accountId
  environment                                                  = var.environment
  oap_frontend_project_source_type                             = var.oap_frontend_project_source_type
  oap_frontend_project_build_timeout                           = var.oap_frontend_project_build_timeout
  oap_frontend_project_environment_compute_type                = var.oap_frontend_project_environment_compute_type
  oap_frontend_project_environment_image                       = var.oap_frontend_project_environment_image
  oap_frontend_project_environment_type                        = var.oap_frontend_project_environment_type
  oap_frontend_project_environment_image_pull_credentials_type = var.oap_frontend_project_environment_image_pull_credentials_type
  oap_frontend_project_artifact_type                           = var.oap_frontend_project_artifact_type
}
module "codepipeline" {
  environment_tag                                     = var.environment_tag
  source                                              = "./modules/codepipeline"
  region                                              = var.region
  accountId                                           = var.accountId
  environment                                         = var.environment
  oap_frontend_pipeline_artifact_store_location       = var.oap_frontend_pipeline_artifact_store_location
  oap_frontend_pipeline_artifact_store_type           = var.oap_frontend_pipeline_artifact_store_type
  oap_frontend_pipeline_source_config_repository_name = var.oap_frontend_pipeline_source_config_repository_name
  oap_frontend_pipeline_source_config_branch_name     = var.oap_frontend_pipeline_source_config_branch_name
  oap_frontend_pipeline_project_name                  = var.oap_frontend_pipeline_project_name
}

module "coludfront" {
  source                                                            = "./modules/cloudfront"
  region                                                            = var.region
  environment                                                       = var.environment
  environment_tag                                                   = var.environment_tag
  s3_distribution_oap_frontend_domain_name                          = var.s3_distribution_oap_frontend_domain_name
  s3_distribution_oap_frontend_origin_id                            = var.s3_distribution_oap_frontend_origin_id
  s3_distribution_http_port                                         = var.s3_distribution_http_port
  s3_distribution_https_port                                        = var.s3_distribution_https_port
  s3_distribution_origin_protocol_policy                            = var.s3_distribution_origin_protocol_policy
  s3_distribution_origin_ssl_protocols                              = var.s3_distribution_origin_ssl_protocols
  s3_distribution_enabled                                           = var.s3_distribution_enabled
  s3_distribution_is_ipv6_enabled                                   = var.s3_distribution_is_ipv6_enabled
  s3_distribution_default_cache_behavior_allowed_methods            = var.s3_distribution_default_cache_behavior_allowed_methods
  s3_distribution_default_cache_behavior_cached_methods             = var.s3_distribution_default_cache_behavior_cached_methods
  s3_distribution_apphero_default_cache_behavior_target_origin_id   = var.s3_distribution_apphero_default_cache_behavior_target_origin_id
  s3_distribution_default_cache_behavior_cache_policy_id            = var.s3_distribution_default_cache_behavior_cache_policy_id
  s3_distribution_default_cache_behavior_response_headers_policy_id = var.s3_distribution_default_cache_behavior_response_headers_policy_id
  s3_distribution_viewer_protocol_policy                            = var.s3_distribution_viewer_protocol_policy
  s3_distribution_geo_restriction_restriction_type                  = var.s3_distribution_geo_restriction_restriction_type
  s3_distribution_viewer_certificate_acm_certificate_arn            = var.s3_distribution_viewer_certificate_acm_certificate_arn
  s3_distribution_viewer_certificate_cloudfront_default_certificate = var.s3_distribution_viewer_certificate_cloudfront_default_certificate



}

module "oap_apigateway" {
  source                                      = "./modules/oapapigateway"
  region                                      = var.region
  environment                                 = var.environment
  environment_tag                             = var.environment_tag
  accountId                                   = var.accountId
  oap_gateway_custom_domain                   = var.oap_gateway_custom_domain
  oap_gateway_certificate_acm_certificate_arn = var.oap_gateway_certificate_acm_certificate_arn
}

module "sqs" {
  source                = "./modules/sqs"
  environment_tag       = var.environment_tag
  canada_central_region = var.canada_central_region
  ireland_region        = var.ireland_region
  accountId             = var.accountId
}

module "amplify" {
  accountId                                              = var.accountId
  source                                                 = "./modules/amplify"
  region                                                 = var.region
  environment                                            = var.environment
  lim_oap_custom_domian_name                             = var.lim_oap_custom_domian_name
  ibat_oap_custom_domian_name                            = var.ibat_oap_custom_domian_name
  hzu_oap_custom_domian_name                             = var.hzu_oap_custom_domian_name
  unfc_oap_custom_domian_name                            = var.unfc_oap_custom_domian_name
  hzu_student_oap_custom_domian_name                     = var.hzu_student_oap_custom_domian_name
  ucw_student_oap_custom_domian_name                     = var.ucw_student_oap_custom_domian_name
  ucw_agent_oap_custom_domian_name                       = var.ucw_agent_oap_custom_domian_name
  oap_gateway_custom_domain                              = var.oap_gateway_custom_domain
  s3_distribution_viewer_certificate_acm_certificate_arn = var.s3_distribution_viewer_certificate_acm_certificate_arn
  lim_student_oap_custom_domian_name                     = var.lim_student_oap_custom_domian_name
  unfc_student_oap_custom_domian_name                    = var.unfc_student_oap_custom_domian_name
}

module "cloudwatch" {
  source          = "./modules/cloudwatch"
  region          = var.region
  environment     = var.environment
  environment_tag = var.environment_tag
}

module "ecs-lim-oap" {
  source      = "./modules/ecs-lim-oap"
  region      = var.region
  environment = var.environment
  accountId   = var.accountId
  s3_role_arn = var.s3_role_arn
}

module "gus-oap-platform-event-listener" {
  source      = "./modules/ecs-gus-oap-platform-event-listener"
  region      = var.region
  environment = var.environment
  accountId   = var.accountId
  environment_tag = var.environment_tag
}

module "iam-users" {
  source          = "./modules/iam-users"
  region          = var.region
  environment     = var.environment
  environment_tag = var.environment_tag
}

module "vpc" {
  source      = "./modules/vpc"
  region      = var.region
  environment = var.environment
  accountId   = var.accountId
}

module "cognito" {
  source                                                                = "./modules/cognito"
  region                                                                = var.region
  environment                                                           = var.environment
  environment_tag                                                       = var.environment_tag
  accountId                                                             = var.accountId
  cognito_linkedin_identity_provider_client_id                          = var.cognito_linkedin_identity_provider_client_id
  cognito_linkedin_identity_provider_client_secret                      = var.cognito_linkedin_identity_provider_client_secret
  cognito_linkedin_identity_provider_oidc_issuer                        = var.cognito_linkedin_identity_provider_oidc_issuer
  cognito_linkedin_identity_provider_authorize_scopes                   = var.cognito_linkedin_identity_provider_authorize_scopes
  cognito_linkedin_identity_provider_attributes_request_method          = var.cognito_linkedin_identity_provider_attributes_request_method
  hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows          = var.hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows
  hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = var.hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes
  hzu_student_oap_cognito_user_pool_client_explicit_auth_flows          = var.hzu_student_oap_cognito_user_pool_client_explicit_auth_flows
  hzu_student_oap_cognito_user_pool_client_callback_urls                = var.hzu_student_oap_cognito_user_pool_client_callback_urls
  hzu_student_oap_cognito_user_pool_client_logout_urls                  = var.hzu_student_oap_cognito_user_pool_client_logout_urls
  hzu_student_oap_cognito_user_pool_client_supported_identity_providers = var.hzu_student_oap_cognito_user_pool_client_supported_identity_providers
  ses_mailer_arn                                                        = var.ses_mailer_arn
  ucw_ses_mailer_arn                                                    = var.ucw_ses_mailer_arn
  ueg_ses_mailer_arn                                                     = var.ueg_ses_mailer_arn
  hzu                                                                   = var.hzu
  user_type_student                                                     = var.user_type_student
  lim_student_oap_cognito_user_pool_client_allowed_oauth_flows          = var.lim_student_oap_cognito_user_pool_client_allowed_oauth_flows
  lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = var.lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes
  lim_student_oap_cognito_user_pool_client_explicit_auth_flows          = var.lim_student_oap_cognito_user_pool_client_explicit_auth_flows
  lim_student_oap_cognito_user_pool_client_callback_urls                = var.lim_student_oap_cognito_user_pool_client_callback_urls
  lim_student_oap_cognito_user_pool_client_logout_urls                  = var.lim_student_oap_cognito_user_pool_client_logout_urls
  lim_student_oap_cognito_user_pool_client_supported_identity_providers = var.lim_student_oap_cognito_user_pool_client_supported_identity_providers
  lim                                                                   = var.lim
  ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows          = var.ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows
  ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = var.ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes
  ucw_student_oap_cognito_user_pool_client_explicit_auth_flows          = var.ucw_student_oap_cognito_user_pool_client_explicit_auth_flows
  ucw_student_oap_cognito_user_pool_client_callback_urls                = var.ucw_student_oap_cognito_user_pool_client_callback_urls
  ucw_student_oap_cognito_user_pool_client_logout_urls                  = var.ucw_student_oap_cognito_user_pool_client_logout_urls
  ucw_student_oap_cognito_user_pool_client_supported_identity_providers = var.ucw_student_oap_cognito_user_pool_client_supported_identity_providers
  ucw                                                                   = var.ucw
  unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows         = var.unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows
  unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes        = var.unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes
  unfc_student_oap_cognito_user_pool_client_explicit_auth_flows         = var.unfc_student_oap_cognito_user_pool_client_explicit_auth_flows
  unfc_student_oap_cognito_user_pool_client_callback_urls               = var.unfc_student_oap_cognito_user_pool_client_callback_urls
  unfc_student_oap_cognito_user_pool_client_logout_urls                 = var.unfc_student_oap_cognito_user_pool_client_logout_urls
  unfc_student_oap_cognito_user_pool_client_supported_identity_providers = var.unfc_student_oap_cognito_user_pool_client_supported_identity_providers
  unfc                                                                  = var.unfc
  ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows          = var.ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows
  ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = var.ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes
  ueg_student_oap_cognito_user_pool_client_explicit_auth_flows          = var.ueg_student_oap_cognito_user_pool_client_explicit_auth_flows
  ueg_student_oap_cognito_user_pool_client_callback_urls                = var.ueg_student_oap_cognito_user_pool_client_callback_urls
  ueg_student_oap_cognito_user_pool_client_logout_urls                  = var.ueg_student_oap_cognito_user_pool_client_logout_urls
  ueg_student_oap_cognito_user_pool_client_supported_identity_providers = var.ueg_student_oap_cognito_user_pool_client_supported_identity_providers
  ueg                                                                   = var.ueg
}

module "lambda" {
  source = "./modules/lambda"
  region            = var.region
  accountId         = var.accountId
  environment       = var.environment
  environment_tag   = var.environment_tag
  hzu_agent_app_id  = var.hzu_agent_app_id
  lsbfmyr_agent_app_id = var.lsbfmyr_agent_app_id
  hzu_student_app_id = var.hzu_student_app_id
  unfc_student_app_id = var.unfc_student_app_id
  unfc_agent_app_id = var.unfc_agent_app_id
  ucw_agent_app_id = var.ucw_agent_app_id
  ucw_student_app_id = var.ucw_student_app_id
  lim_agent_app_id = var.lim_agent_app_id
  lim_student_app_id = var.lim_student_app_id
  ibat_agent_app_id = var.ibat_agent_app_id
  ueg_agent_app_id = var.ueg_agent_app_id
  ueg_student_app_id = var.ueg_student_app_id
  ard_agent_app_id = var.ard_agent_app_id
  wul_agent_app_id = var.wul_agent_app_id
  pu_agent_app_id = var.pu_agent_app_id
}

module "codecommit" {
  source = "./modules/codecommit"
  region            = var.region
  accountId         = var.accountId
  environment       = var.environment
}

module "ses" {
  source = "./modules/ses"
  region = var.region
  lsbfm_email_identity = var.lsbfm_email_identity
  ucw_email_identity   = var.ucw_email_identity
  ueg_email_identity    = var.ueg_email_identity
  unfc_email_identity  = var.unfc_email_identity
}

module "waf" {
  source                     = "./modules/waf"
  region                     = var.region
  accountId                  = var.accountId
  environment                = var.environment
  api_gateway_arn            = var.api_gateway_arn
  amplify_waf_log_group_arn  = module.cloudwatch.amplify_waf_log_group_arn
  backend_waf_log_group_arn  = module.cloudwatch.backend_waf_log_group_arn
}

module "ecr" {
  source          = "./modules/ecr"
  environment       = var.environment
}