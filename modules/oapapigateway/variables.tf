variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "oap_gateway_custom_domain" {
  description = ""
  type        = string
}

variable "oap_gateway_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}