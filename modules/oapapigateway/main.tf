provider "aws" {
  region = var.region
}

resource "aws_api_gateway_rest_api" "api" {
  name        = "oap-service-${var.environment}"
  description = "API"
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_authorizer" "lambda_authorizer" {
  name            = "oap-custom-lambda-authorizer-${var.environment}"
  type            = "REQUEST"
  rest_api_id     = aws_api_gateway_rest_api.api.id
  identity_source = "method.request.header.Authorization,method.request.header.X-Api-Key"
  authorizer_uri  = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-custom-authorizer-${var.environment}/invocations"
  authorizer_credentials = "arn:aws:iam::${var.accountId}:role/oap-lambda-authorizer-role-${var.environment}"
  authorizer_result_ttl_in_seconds = 0
}

resource "aws_api_gateway_method" "method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_rest_api.api.root_resource_id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_rest_api.api.root_resource_id
  http_method          = aws_api_gateway_method.method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#OAP
resource "aws_api_gateway_resource" "oap_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "oap"
}
resource "aws_api_gateway_method" "oap_root_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.oap_resource.id
  http_method   = "ANY"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "oap_root_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.oap_resource.id
  http_method             = aws_api_gateway_method.oap_root_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "oap_root_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.oap_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "oap_root_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.oap_resource.id
  http_method          = aws_api_gateway_method.oap_root_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "oap_root_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.oap_resource.id
  http_method = aws_api_gateway_method.oap_root_options_method.http_method
  status_code = "200"
  response_models = {
        "application/json" = "Empty"
    }
  response_parameters = {
        "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
    }
}

resource "aws_api_gateway_integration_response" "oap_root_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.oap_resource.id
  http_method = aws_api_gateway_method.oap_root_options_method.http_method
  status_code = aws_api_gateway_method_response.oap_root_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.oap_root_options_integration
    ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin": "'*'"
    }
  response_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}
resource "aws_api_gateway_resource" "oap_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.oap_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "oap_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.oap_proxy_resource.id
  http_method   = "ANY"
  authorization = "None"
}

resource "aws_api_gateway_integration" "oap_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.oap_proxy_resource.id
  http_method             = aws_api_gateway_method.oap_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "oap_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.oap_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "oap_proxy_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.oap_proxy_resource.id
  http_method          = aws_api_gateway_method.oap_proxy_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "oap_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.oap_proxy_resource.id
  http_method = aws_api_gateway_method.oap_proxy_options_method.http_method
  status_code = "200"
  response_models = {
        "application/json" = "Empty"
    }
  response_parameters = {
        "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
    }
}

resource "aws_api_gateway_integration_response" "oap_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.oap_proxy_resource.id
  http_method = aws_api_gateway_method.oap_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.oap_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.oap_proxy_options_integration
    ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin": "'*'"
    }
  response_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_rest_api.api.root_resource_id
  http_method = aws_api_gateway_method.method.http_method
  status_code = aws_api_gateway_method_response.method_response.status_code
  depends_on = [
    aws_api_gateway_integration.integration
  ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }

  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#pdf
resource "aws_api_gateway_resource" "generate_pdf_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.oap_resource.id
  path_part   = "generatepdf"
}
resource "aws_api_gateway_method" "generate_pdf_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.generate_pdf_resource.id
  http_method   = "POST"
  authorization = "NONE"
}
resource "aws_api_gateway_integration" "generate_pdf_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.generate_pdf_resource.id
  http_method             = aws_api_gateway_method.generate_pdf_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-pdf-generation-${var.environment}/invocations"
}
resource "aws_api_gateway_method" "generate_pdf_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.generate_pdf_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "generate_pdf_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.generate_pdf_resource.id
  http_method          = aws_api_gateway_method.generate_pdf_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "generate_pdf_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.generate_pdf_resource.id
  http_method = aws_api_gateway_method.generate_pdf_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "generate_pdf_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.generate_pdf_resource.id
  http_method = aws_api_gateway_method.generate_pdf_options_method.http_method
  status_code = aws_api_gateway_method_response.generate_pdf_options_method_response.status_code
  # depends_on = [
  #   aws_api_gateway_integration.generate_pdf_options_integration
  # ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#hzupdf
resource "aws_api_gateway_resource" "generate_enrollment_pdf_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.oap_resource.id
  path_part   = "generateenrollmentpdf"
}
resource "aws_api_gateway_method" "generate_enrollment_pdf_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method   = "POST"
  authorization = "NONE"
}
resource "aws_api_gateway_integration" "generate_enrollment_pdf_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method             = aws_api_gateway_method.generate_enrollment_pdf_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:hzu-enrollment-pdf-generation-${var.environment}/invocations"
}
resource "aws_api_gateway_method" "generate_enrollment_pdf_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "generate_enrollment_pdf_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method          = aws_api_gateway_method.generate_enrollment_pdf_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}

resource "aws_api_gateway_method_response" "generate_enrollment_pdf_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method = aws_api_gateway_method.generate_enrollment_pdf_options_method.http_method
  status_code = "200"
  response_models = {
    "application/json" = "Empty"
  }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
  }
}

resource "aws_api_gateway_integration_response" "generate_enrollment_pdf_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.generate_enrollment_pdf_resource.id
  http_method = aws_api_gateway_method.generate_enrollment_pdf_options_method.http_method
  status_code = aws_api_gateway_method_response.generate_enrollment_pdf_options_method_response.status_code
  # depends_on = [
  #   aws_api_gateway_integration.generate_enrollment_pdf_options_integration
  # ]
  response_parameters = {
    "method.response.header.Access-Control-Allow-Methods" : "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
    "method.response.header.Access-Control-Allow-Headers" : "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
    "method.response.header.Access-Control-Allow-Origin" : "'*'"
  }
  response_templates = {
    "application/json" = jsonencode({
      statusCode = 200
    })
  }
}
#student portal apis
resource "aws_api_gateway_resource" "student_api_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_rest_api.api.root_resource_id
  path_part   = "student"
}
resource "aws_api_gateway_resource" "student_oap_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_api_resource.id
  path_part   = "oap"
}
resource "aws_api_gateway_method" "student_oap_root_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_oap_resource.id
  http_method   = "ANY"
  authorization = "CUSTOM"
  request_parameters = {
    "method.request.path.proxy" = true
    "method.request.header.Authorization" = true
    "method.request.header.X-Api-Key" = true
  }
  authorizer_id        = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "student_oap_root_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_oap_resource.id
  http_method             = aws_api_gateway_method.student_oap_root_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_oap_root_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_oap_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}


resource "aws_api_gateway_integration" "student_oap_root_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_oap_resource.id
  http_method          = aws_api_gateway_method.student_oap_root_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "student_oap_root_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_oap_resource.id
  http_method = aws_api_gateway_method.student_oap_root_options_method.http_method
  status_code = "200"
  response_models = {
        "application/json" = "Empty"
    }
  response_parameters = {
    "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
    }
}

resource "aws_api_gateway_integration_response" "student_oap_root_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_oap_resource.id
  http_method = aws_api_gateway_method.student_oap_root_options_method.http_method
  status_code = aws_api_gateway_method_response.student_oap_root_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_oap_root_options_integration
    ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin": "'*'"
    }
  response_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}
resource "aws_api_gateway_resource" "student_oap_proxy_resource" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  parent_id   = aws_api_gateway_resource.student_oap_resource.id
  path_part   = "{proxy+}"
}

resource "aws_api_gateway_method" "student_oap_proxy_any_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method   = "ANY"
  authorization = "CUSTOM"
  request_parameters = {
    "method.request.path.proxy" = true
    "method.request.header.Authorization" = true
    "method.request.header.X-Api-Key" = true
  }
  authorizer_id        = aws_api_gateway_authorizer.lambda_authorizer.id
}

resource "aws_api_gateway_integration" "student_oap_proxy_any_integration" {
  rest_api_id             = aws_api_gateway_rest_api.api.id
  resource_id             = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method             = aws_api_gateway_method.student_oap_proxy_any_method.http_method
  type                    = "AWS_PROXY"
  integration_http_method = "POST"
  uri                     = "arn:aws:apigateway:${var.region}:lambda:path/2015-03-31/functions/arn:aws:lambda:${var.region}:${var.accountId}:function:oap-backend-service-${var.environment}/invocations"
}

resource "aws_api_gateway_method" "student_oap_proxy_options_method" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  resource_id   = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method   = "OPTIONS"
  authorization = "NONE"
}

resource "aws_api_gateway_integration" "student_oap_proxy_options_integration" {
  rest_api_id          = aws_api_gateway_rest_api.api.id
  resource_id          = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method          = aws_api_gateway_method.student_oap_proxy_options_method.http_method
  type                 = "MOCK"
  passthrough_behavior = "WHEN_NO_MATCH"
  request_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}

resource "aws_api_gateway_method_response" "student_oap_proxy_options_method_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method = aws_api_gateway_method.student_oap_proxy_options_method.http_method
  status_code = "200"
  response_models = {
        "application/json" = "Empty"
    }
  response_parameters = {
        "method.response.header.Access-Control-Allow-Headers" = true
    "method.response.header.Access-Control-Allow-Methods" = true
    "method.response.header.Access-Control-Allow-Origin"  = true
    }
}

resource "aws_api_gateway_integration_response" "student_oap_proxy_options_integration_response" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  resource_id = aws_api_gateway_resource.student_oap_proxy_resource.id
  http_method = aws_api_gateway_method.student_oap_proxy_options_method.http_method
  status_code = aws_api_gateway_method_response.student_oap_proxy_options_method_response.status_code
  depends_on = [
    aws_api_gateway_integration.student_oap_proxy_options_integration
    ]
  response_parameters = {
        "method.response.header.Access-Control-Allow-Methods": "'DELETE,GET,HEAD,OPTIONS,PATCH,POST,PUT'",
        "method.response.header.Access-Control-Allow-Headers": "'Content-Type,Authorization,X-Amz-Date,X-Api-Key,X-Amz-Security-Token'",
        "method.response.header.Access-Control-Allow-Origin": "'*'"
    }
  response_templates = {
        "application/json" = jsonencode({
      statusCode = 200
        })
    }
}
#gateway response
resource "aws_api_gateway_gateway_response" "access_denied_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "ACCESS_DENIED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "unauthorized_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "UNAUTHORIZED"
  status_code   = 401
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "waf_filtered_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "WAF_FILTERED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "request_too_large_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "REQUEST_TOO_LARGE"
  status_code   = 413
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_apikey_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_API_KEY"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_4xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_4XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "quota_exceeded_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "QUOTA_EXCEEDED"
  status_code   = 429
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "throttled_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "THROTTLED"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}



resource "aws_api_gateway_gateway_response" "bad_request_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_BODY"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "invalid_signature_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "INVALID_SIGNATURE"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "missing_athentication_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "MISSING_AUTHENTICATION_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "bad_request_parameter_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "BAD_REQUEST_PARAMETERS"
  status_code   = 400
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "default_5xx_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "DEFAULT_5XX"
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "expired_token_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "EXPIRED_TOKEN"
  status_code   = 403
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}

resource "aws_api_gateway_gateway_response" "resource_not_found_response" {
  rest_api_id   = aws_api_gateway_rest_api.api.id
  response_type = "RESOURCE_NOT_FOUND"
  status_code   = 404
  response_parameters = {
    "gatewayresponse.header.Access-Control-Allow-Methods" = "'OPTIONS,POST'",
    "gatewayresponse.header.Access-Control-Allow-Headers" = "'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token'",
    "gatewayresponse.header.Access-Control-Allow-Origin"  = "'*'"
  }
  response_templates = {
    "application/json" = "{\"message\": \"$context.error.messageString\"}"
  }
}
resource "aws_api_gateway_method_settings" "oap_gateway_settings" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  stage_name  = var.environment
  method_path = "*/*"
  settings {
    logging_level = "INFO"
    data_trace_enabled = true
    metrics_enabled = true
  }
}
resource "aws_api_gateway_deployment" "oap_deployment" {
  rest_api_id = aws_api_gateway_rest_api.api.id
  variables = {
    deployed_at = timestamp()
  }
  triggers = {
    redeployment = sha1(jsonencode(aws_api_gateway_rest_api.api.body))
  }
  lifecycle {
    create_before_destroy = true
  }
  depends_on = [
    aws_api_gateway_method.method,
    aws_api_gateway_integration.integration,
    aws_api_gateway_method.oap_root_method,
    aws_api_gateway_method.oap_root_options_method,
    aws_api_gateway_method.oap_proxy_any_method,
    aws_api_gateway_method.oap_proxy_options_method,
    aws_api_gateway_integration.oap_root_integration,
    aws_api_gateway_integration.oap_root_options_integration,
    aws_api_gateway_integration.oap_proxy_any_integration,
    aws_api_gateway_integration.oap_proxy_options_integration,
    aws_api_gateway_method.student_oap_root_method,
    aws_api_gateway_method.student_oap_root_options_method,
    aws_api_gateway_method.student_oap_proxy_any_method,
    aws_api_gateway_method.student_oap_proxy_options_method,
    aws_api_gateway_integration.student_oap_root_integration,
    aws_api_gateway_integration.student_oap_root_options_integration,
    aws_api_gateway_integration.student_oap_proxy_any_integration,
    aws_api_gateway_integration.student_oap_proxy_options_integration,
    aws_api_gateway_method.generate_enrollment_pdf_method,
    aws_api_gateway_method.generate_enrollment_pdf_options_method,
    aws_api_gateway_integration.generate_enrollment_pdf_integration,
    aws_api_gateway_integration.generate_enrollment_pdf_options_integration
  ]
}
resource "aws_api_gateway_stage" "oap_deployment_stage" {
  deployment_id        = aws_api_gateway_deployment.oap_deployment.id
  stage_name           = var.environment
  rest_api_id          = aws_api_gateway_rest_api.api.id
  xray_tracing_enabled = true
  tags = {
    Environment = var.environment_tag
    Project     = "OAP"
    Team        = "EIP Development Team"
  }
}
resource "aws_api_gateway_domain_name" "oap_domain" {
  domain_name     = var.oap_gateway_custom_domain
  certificate_arn = var.oap_gateway_certificate_acm_certificate_arn
  endpoint_configuration {
    types = ["EDGE"]
  }
}
resource "aws_api_gateway_base_path_mapping" "oap_gateway_mapping" {
  api_id      =   aws_api_gateway_rest_api.api.id
  domain_name = aws_api_gateway_domain_name.oap_domain.id
  stage_name  = aws_api_gateway_stage.oap_deployment_stage.stage_name
}
output "invoke_url" {
  value = aws_api_gateway_deployment.oap_deployment.invoke_url
}
