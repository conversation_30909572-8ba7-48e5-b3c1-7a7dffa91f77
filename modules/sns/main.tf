provider "aws" {
  region = var.region
}
resource "aws_sns_topic" "gus_oap_sf_topic" {
  name                        = "${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
  display_name                = "gus_oap_sf_sns_topic"
  fifo_topic                  = true
  content_based_deduplication = true
}

resource "aws_sns_topic_subscription" "gus_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-IBAT-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["IBAT_EL"],
  })
}

resource "aws_sns_topic_subscription" "gus_lim_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-LIM-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["LIM"],
  })
}

resource "aws_sns_topic_subscription" "gus_hzu_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-HZU-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["HZU"],
  })
}

//hzu cr subscription
resource "aws_sns_topic_subscription" "gus_hzu_cr_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-HZU-EIP-CR-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["CR_HZU"],
  })
}

resource "aws_sns_topic_subscription" "gus_unfc_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-UNFC-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["UNFC"],
  })
}

resource "aws_sns_topic_subscription" "gus_ucw_agent_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-UCW-AGENT-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["UCW"],
  })
}

resource "aws_sns_topic_subscription" "gus_lsbfmyr_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-LSBFMYR-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["LSBFMYR"],
  })
}

resource "aws_sns_topic_subscription" "gus_ueg_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-UEG-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["UEG"],
  })
}
//ARD
resource "aws_sns_topic_subscription" "gus_ard_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-ARD-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["ARD"],
  })
}
//WUL
resource "aws_sns_topic_subscription" "gus_wul_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-WUL-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["WUL"],
  })
}

//PU
resource "aws_sns_topic_subscription" "gus_pu_oap_sf_topic_subscribe" {
  topic_arn            = aws_sns_topic.gus_oap_sf_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-PU-EIP-QUEUE.fifo"

  filter_policy = jsonencode({
    source = ["PU"],
  })
}

resource "aws_sns_topic" "gus_oap_student_notification_topic" {
  name                        = "${var.environment_tag}-GUS-OAP-STUDENT-NOTIFICATION-TOPIC"
  display_name                = "gus_oap_student_notification_topic"
  fifo_topic                  = false
  content_based_deduplication = false
}

resource "aws_sns_topic" "sys_outbound_topic" {
  name                        = "${var.environment_tag}-SYS-OUTBOUND-TOPIC.fifo"
  display_name                = "sys_outbound_sns_topic"
  fifo_topic                  = true
  content_based_deduplication = true
}

resource "aws_sns_topic" "ucw_legacy_application_topic" {
  name                        = "${var.environment_tag}-UCW-LEGACY-APPLICATION-TOPIC.fifo"
  display_name                = "ucw_legacy_application_topic"
  fifo_topic                  = true
  content_based_deduplication = true
}
resource "aws_sns_topic_subscription" "ucw_legacy_application_topic_subscribe" {
  topic_arn            = aws_sns_topic.ucw_legacy_application_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-UCW-LEGACY-APPLICATION-QUEUE.fifo"
}

resource "aws_sns_topic_subscription" "sys_outbound_topic_subscribe" {
  topic_arn            = aws_sns_topic.sys_outbound_topic.arn
  raw_message_delivery = true
  protocol             = "sqs"
  endpoint             = "arn:aws:sqs:${var.region}:${var.accountId}:${var.environment_tag}-BSBI-EIP-SYS-INBOUND-QUEUE.fifo"
}