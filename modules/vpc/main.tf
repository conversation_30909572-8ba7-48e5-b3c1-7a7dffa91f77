provider "aws" {
  region = var.region
}

data "aws_availability_zones" "available" {}

resource "aws_vpc" "main" {
 cidr_block           = var.cidr_blocks[var.environment]
 enable_dns_support   = true
 enable_dns_hostnames = true
 tags = {
   Name = "${var.environment}-vpc"
 }
}

resource "aws_subnet" "public_subnets" {
  count = 2
  vpc_id             =  aws_vpc.main.id
  cidr_block         = var.public_subnet_cidrs[var.environment][count.index]
  availability_zone  = element(data.aws_availability_zones.available.names, count.index)
  tags = {
    Name = "${var.environment}-public-subnet"
 }
}
 
resource "aws_subnet" "private_subnets" {
  count = 2
  vpc_id              = aws_vpc.main.id
  cidr_block          = var.private_subnet_cidrs[var.environment][count.index]
  availability_zone   = element(data.aws_availability_zones.available.names, count.index)
  tags = {
   Name = "${var.environment}-private-subnet"
 }
}

resource "aws_internet_gateway" "gw" {
 #for_each     = aws_vpc.main
 vpc_id       = aws_vpc.main.id
 tags = {
   Name = "${var.environment}-vpc-ig"
 }
}

resource "aws_route_table" "public_route_table" {
 vpc_id     = aws_vpc.main.id
 route {
   cidr_block = "0.0.0.0/0"
   gateway_id =  aws_internet_gateway.gw.id
 }
 tags = {
   Name = "${var.environment}-route table"
 }
}

resource "aws_nat_gateway" "nat" {
  count           = 2
  allocation_id   = aws_eip.eip[count.index].id
  subnet_id       = aws_subnet.public_subnets[count.index].id
}

resource "aws_eip" "eip" {
  count = 2
}

resource "aws_route_table" "private_route_table" {
 count = 2
 vpc_id     = aws_vpc.main.id
 route {
   cidr_block = "0.0.0.0/0"
   nat_gateway_id = aws_nat_gateway.nat[count.index].id
 }
 tags = {
   Name = "${var.environment}-route table"
 }
}

resource "aws_route_table_association" "public_subnet_association" {
  count       = 2
  subnet_id         = aws_subnet.public_subnets[count.index].id
  route_table_id    = aws_route_table.public_route_table.id
}

resource "aws_route_table_association" "private_subnet_association" {
  count         = 2
  subnet_id     = aws_subnet.private_subnets[count.index].id
  route_table_id = aws_route_table.private_route_table[count.index].id
}

resource "aws_security_group" "oap_exporter_security_group" {
  vpc_id = aws_vpc.main.id
  name   = "oap-exporter-sg-${var.environment}"
  // Egress rules (outgoing traffic)
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
  tags = {
    Name = "oap-exporter-sg-${var.environment}"
  }
}

output "security_group" {
  value =  ["aws_security_group.oap_exporter_security_group.id"]
}

output "subnet_public" {
  value = ["aws_subnet.public_subnets.id"]
}

output "subnet_private" {
  value = ["aws_subnet.private_subnets.id"]
}