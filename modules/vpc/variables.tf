variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Choose your account"
  type        = string
}

#variable "public_subnet_cidrs" {
# type        = list(string)
# description = "Public Subnet CIDR values"
# default     = ["*********/20", "********/20"]
#}
# 
#variable "private_subnet_cidrs" {
# type        = list(string)
# description = "Private Subnet CIDR values"
# default     = ["*********/20", "*********/20"]
#}

#variable "availability_zones" {
#  type = map(list(string))
#  default = {
#    #dev  = ["eu-west-1a", "eu-west-1b"]
#    prod = ["eu-west-1a", "eu-west-1b"]
#  }
#}

#variable "availability_zones" {
#  description = "List of availability zones for the VPC"
#  type        = lsit
#  default     = []
#}

variable "cidr_blocks" {
  type = map(string)
  default = {
    dev       = "********/16"
    prod      = "*********/16"
  }
}

variable "public_subnet_cidrs" {
  type = map(list(string))
  default = {
    dev = ["*********/20", "********/20"]
    prod  = ["*********/24", "*********/24"]
  }
}

variable "private_subnet_cidrs" {
  type = map(list(string))
  default = {
    dev = ["*********/20", "*********/20"]
    prod  = ["*********/24", "*********/24"]
  }
}