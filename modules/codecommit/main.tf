resource "aws_codecommit_trigger" "oap_frontend_lambda_trigger" {
  repository_name = "oap-frontend"

  trigger {
    name            = "oap-frontend-lambda-trigger-${var.environment}"
    events          = ["updateReference", "createReference"]
    branches        = ["lsbfmyr-${var.environment}", "hzu-${var.environment}","hzu-student-${var.environment}","unfc-student-${var.environment}","unfc-${var.environment}","ucw-agent-${var.environment}","ucw-student-${var.environment}","lim-student-${var.environment}","lim-${var.environment}","ibat-${var.environment}"]
    destination_arn = "arn:aws:lambda:${var.region}:${var.accountId}:function:oap-frontend-amplify-apps-${var.environment}"
  }

  trigger {
    name            = "oap-frontend-lambda-trigger-${var.environment}-2"
    events          = ["updateReference", "createReference"]
    branches        = ["ueg-${var.environment}", "ueg-student-${var.environment}", "ard-${var.environment}", "wul-${var.environment}","pu-${var.environment}"]
    destination_arn = "arn:aws:lambda:${var.region}:${var.accountId}:function:oap-frontend-amplify-apps-${var.environment}"
  }
}