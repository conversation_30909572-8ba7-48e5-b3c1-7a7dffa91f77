provider "aws" {
  region = var.canada_central_region
}

resource "aws_sqs_queue" "ucw_eip_sys_outqueue" {
  name                      = "${var.environment_tag}-UCW-EIP-SYS-OUTQUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ucw_sys_eip_inqueue" {
  name                      = "${var.environment_tag}-UCW-SYS-EIP-INQUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

provider "aws" {
  alias  = "ireland"
  region =  var.ireland_region
}

resource "aws_sqs_queue" "bsbi_eip_sys_outqueue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-BSBI-EIP-SYS-OUTQUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "bsbi_sys_eip_inqueue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-BSBI-SYS-EIP-INQUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ibat_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-IBAT-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ibat_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-IBAT-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ibat_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "ibat_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ibat_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.ibat_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue_redrive_allow_policy" "ibat_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ibat_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ibat_eip_queue.arn]
  })
}
resource "aws_sqs_queue" "lim_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-LIM-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "lim_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-LIM-EIP-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.lim_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "lim_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.lim_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.lim_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "lim_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.lim_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.lim_eip_queue.arn]
  })
}

// UNFC
resource "aws_sqs_queue" "unfc_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UNFC-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "unfc_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UNFC-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.unfc_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "unfc_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.unfc_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.unfc_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue_redrive_allow_policy" "unfc_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.unfc_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.unfc_eip_queue.arn]
  })
}

//UEG
resource "aws_sqs_queue" "ueg_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UEG-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ueg_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ueg_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UEG-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "ueg_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ueg_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.ueg_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue_redrive_allow_policy" "ueg_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ueg_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ueg_eip_queue.arn]
  })
}

//ARDEN
resource "aws_sqs_queue" "ard_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-ARD-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ard_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ard_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-ARD-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "ard_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ard_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.ard_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue_redrive_allow_policy" "ard_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ard_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ard_eip_queue.arn]
  })
}

//WUL
resource "aws_sqs_queue" "wul_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-WUL-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.wul_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "wul_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-WUL-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "wul_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.wul_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.wul_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue_redrive_allow_policy" "wul_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.wul_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.wul_eip_queue.arn]
  })
}

//PU

resource "aws_sqs_queue" "pu_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-PU-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.pu_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "pu_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-PU-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "pu_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.pu_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.pu_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "pu_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.pu_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.pu_eip_queue.arn]
  })
}


//LSBFMYR
resource "aws_sqs_queue" "lsbfmyr_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-LSBFMYR-EIP-QUEUE.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.lsbfmyr_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "lsbfmyr_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.lsbfmyr_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.lsbfmyr_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}
resource "aws_sqs_queue" "lsbfmyr_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-LSBFMYR-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_sqs_queue_redrive_allow_policy" "lsbfmyr_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.lsbfmyr_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.lsbfmyr_eip_queue.arn]
  })
}

//hzuQueue
resource "aws_sqs_queue" "hzu_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-HZU-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "hzu_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-HZU-EIP-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.hzu_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "hzu_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.hzu_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.hzu_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "hzu_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.hzu_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.hzu_eip_queue.arn]
  })
}
//CR HZU QUEUE
resource "aws_sqs_queue" "hzu_cr_eip_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-HZU-EIP-CR-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "hzu_cr_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-HZU-EIP-CR-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.hzu_cr_eip_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_sqs_queue_policy" "hzu_cr_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.hzu_cr_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.hzu_cr_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "hzu_cr_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.hzu_cr_eip_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.hzu_cr_eip_queue.arn]
  })
}

//ucwagentqueue
resource "aws_sqs_queue" "ucw_agent_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UCW-AGENT-EIP-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ucw_agent_eip_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UCW-AGENT-EIP-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ucw_agent_dead_letter_queue.arn
    maxReceiveCount     = 1
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "ucw_agent_eip_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ucw_agent_eip_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.ucw_agent_eip_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-GUS-OAP-SF-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "ucw_agent_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ucw_agent_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ucw_agent_eip_queue.arn]
  })
}
//student
resource "aws_sqs_queue" "student_oap_lead_owner_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-STUDENT-OAP-LEAD-OWNER-CHANGE-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "student_oap_lead_owner_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-STUDENT-OAP-LEAD-OWNER-CHANGE-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.student_oap_lead_owner_dead_letter_queue.arn
    maxReceiveCount     = 2
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_redrive_allow_policy" "student_oap_lead_owner_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.student_oap_lead_owner_dead_letter_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.student_oap_lead_owner_queue.arn]
  })
}
//ucw legacy application
resource "aws_sqs_queue" "ucw_legacy_application_dead_letter_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UCW-LEGACY-APPLICATION-DLQ.fifo"
  fifo_queue                = true
  content_based_deduplication = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue" "ucw_legacy_application_queue" {
  provider                  = aws.ireland
  name                      = "${var.environment_tag}-UCW-LEGACY-APPLICATION-QUEUE.fifo"
  fifo_queue                = true
  visibility_timeout_seconds = 180
  message_retention_seconds = 604800
  content_based_deduplication = true
  redrive_policy = jsonencode({
    deadLetterTargetArn = aws_sqs_queue.ucw_legacy_application_dead_letter_queue.arn
    maxReceiveCount     = 4
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_sqs_queue_policy" "ucw_legacy_application_queue_access_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ucw_legacy_application_queue.url

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Sid    = "Allow-SNS-to-Publish-to-SQS"
        Effect = "Allow"
        Principal = {
          Service = "sns.amazonaws.com"
        }
        Action   = "sqs:SendMessage"
        Resource = aws_sqs_queue.ucw_legacy_application_queue.arn
        Condition = {
          ArnEquals = {
            "aws:SourceArn" = "arn:aws:sns:${var.ireland_region}:${var.accountId}:${var.environment_tag}-UCW-LEGACY-APPLICATION-TOPIC.fifo"
          }
        }
      }
    ]
  })
}

resource "aws_sqs_queue_redrive_allow_policy" "ucw_legacy_application_dl_queue_redrive_allow_policy" {
  provider                  = aws.ireland
  queue_url = aws_sqs_queue.ucw_legacy_application_queue.id

  redrive_allow_policy = jsonencode({
    redrivePermission = "byQueue",
    sourceQueueArns   = [aws_sqs_queue.ucw_legacy_application_queue.arn]
  })
}
