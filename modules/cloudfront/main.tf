provider "aws" {
  region = var.region
}

variable "environment" {
  description = "The environment (e.g., dev, prod)"
  type        = string
}

resource "aws_cloudfront_distribution" "s3_oap_frontend_distribution" {
  origin {
    domain_name = var.s3_distribution_oap_frontend_domain_name
    origin_id   = var.s3_distribution_oap_frontend_origin_id
    custom_origin_config {
      http_port              = var.s3_distribution_http_port
      https_port             = var.s3_distribution_https_port
      origin_protocol_policy = var.s3_distribution_origin_protocol_policy
      origin_ssl_protocols   = var.s3_distribution_origin_ssl_protocols
    }
  }

  enabled         = var.s3_distribution_enabled
  is_ipv6_enabled = var.s3_distribution_is_ipv6_enabled
  #   aliases         = var.oap_frontend_alternative_domain
  default_cache_behavior {
    allowed_methods            = var.s3_distribution_default_cache_behavior_allowed_methods
    cached_methods             = var.s3_distribution_default_cache_behavior_cached_methods
    target_origin_id           = var.s3_distribution_apphero_default_cache_behavior_target_origin_id
    cache_policy_id            = var.s3_distribution_default_cache_behavior_cache_policy_id
    response_headers_policy_id = var.s3_distribution_default_cache_behavior_response_headers_policy_id

    viewer_protocol_policy = var.s3_distribution_viewer_protocol_policy
  }

  restrictions {
    geo_restriction {
      restriction_type = var.s3_distribution_geo_restriction_restriction_type
      locations        = []
    }
  }

  tags = {
    Environment = var.environment_tag
    Project     = "APPHERO"
    Team        = "EIP Development Team"
  }

  viewer_certificate {
    acm_certificate_arn            = var.s3_distribution_viewer_certificate_acm_certificate_arn
    cloudfront_default_certificate = var.s3_distribution_viewer_certificate_cloudfront_default_certificate
    ssl_support_method             = "sni-only"
    minimum_protocol_version       = "TLSv1.2_2021"
  }
}
