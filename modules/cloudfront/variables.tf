variable "region" {
  description = "Choose your region"
  type        = string
}

variable "s3_distribution_oap_frontend_domain_name" {
  description = ""
  type        = string
}

variable "s3_distribution_oap_frontend_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_http_port" {
  description = ""
  type        = number
}

variable "s3_distribution_https_port" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_ssl_protocols" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_is_ipv6_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_default_cache_behavior_allowed_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_cached_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_apphero_default_cache_behavior_target_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_cache_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_response_headers_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_geo_restriction_restriction_type" {
  description = ""
  type        = string
}

variable "environment_tag" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_cloudfront_default_certificate" {
  description = ""
  type        = bool
}


