provider "aws" {
  region = var.region
}

// oap-backend
resource "aws_codepipeline" "oap-backend-pipeline" {
  name     = "oap-backend-service-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/oap-pipeline-access-${var.environment}"
  artifact_store {
    location = "oap-backend-service-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = "oap-backend"
        BranchName           = var.environment
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "oap-backend-service-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_rule" "oap_backend_codecommit_trigger_rule" {
  name = "oapCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:oap-backend"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "oap_backend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.oap_backend_codecommit_trigger_rule.name
  target_id = "oapBackendCodecommitEventTarget"
  arn       = aws_codepipeline.oap-backend-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/oap-codePipeline-start-access-${var.environment}"
}

//oap-handler
resource "aws_codepipeline" "oap-handler-pipeline" {
  name     = "oap-handler-service-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/oap-pipeline-access-${var.environment}"
  artifact_store {
    location = "oap-handler-service-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = "oap-handlers"
        BranchName           = var.environment
        PollForSourceChanges = "false"
        OutputArtifactFormat = "CODEBUILD_CLONE_REF"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "oap-handler-service-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "oap_handler_codecommit_trigger_rule" {
  name = "oaphandlerCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:oap-handlers"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "oap_handler_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.oap_handler_codecommit_trigger_rule.name
  target_id = "oapHandlerCodecommitEventTarget"
  arn       = aws_codepipeline.oap-handler-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/oap-codePipeline-start-access-${var.environment}"
}

//oap-pdf-generator
resource "aws_codepipeline" "oap-pdf-generator-pipeline" {
  name     = "oap-pdf-generator-${var.environment}"
  role_arn = "arn:aws:iam::${var.accountId}:role/oap-pipeline-access-${var.environment}"
  artifact_store {
    location = "oap-pdf-generator-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["SourceArtifact"]

      configuration = {
        RepositoryName       = "oap-pdf-generator"
        BranchName           = var.environment
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["SourceArtifact"]
      output_artifacts = ["BuildArtifact"]

      configuration = {
        ProjectName = "oap-pdf-generator-${var.environment}"
        EnvironmentVariables = jsonencode([{
          name  = "stage",
          value = "${var.environment}",
          type  = "PLAINTEXT"
        }])
      }
    }
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_rule" "oappdf_generator_codecommit_trigger_rule" {
  name = "oapPdfGeneratorCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:oap-pdf-generator"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_cloudwatch_event_target" "oap_pdf_generator_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.oappdf_generator_codecommit_trigger_rule.name
  target_id = "oapPdfGeneratorCodecommitEventTarget"
  arn       = aws_codepipeline.oap-pdf-generator-pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/oap-codePipeline-start-access-${var.environment}"
}
