variable "region" {
  description = "Aws region"
  type        = string

}
variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}
variable "oap_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_project_name" {
  description = ""
  type        = string
}