provider "aws" {
  region = var.region
}

//oap-backend
resource "aws_cloudwatch_log_group" "oap_backend_project_logs" {
  name              = "build-logs-oap-backend-project"  # Name for your custom log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed
}

resource "aws_codebuild_project" "oap-backend-project" {
  name          = "oap-backend-service-${var.environment}"
  description   = ""
  build_timeout = "8"
  service_role  = "arn:aws:iam::${var.accountId}:role/oap-codebuild-access-${var.environment}"

  source {
    type = "CODEPIPELINE"
  }

  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
      group_name = aws_cloudwatch_log_group.oap_backend_project_logs.name  # Reference to the custom log group
    }
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
//oap-handlers
resource "aws_cloudwatch_log_group" "oap_handler_project_logs" {
  name              = "build-logs-oap-handler-project"  # Name for your custom log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed
}

resource "aws_codebuild_project" "oap-handler-project" {
  name          = "oap-handler-service-${var.environment}"
  description   = ""
  build_timeout = "5"
  service_role  = "arn:aws:iam::${var.accountId}:role/oap-codebuild-access-${var.environment}"

  source {
    type = "CODEPIPELINE"
  }

  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
      group_name = aws_cloudwatch_log_group.oap_handler_project_logs.name  # Reference to the custom log group
    }
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
//oap-pdf-generator
resource "aws_cloudwatch_log_group" "oap_pdf_generator_logs" {
  name              = "build-logs-oap-pdf-generator"  # Name for your custom log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed
}

resource "aws_codebuild_project" "oap-pdf-generator" {
  name          = "oap-pdf-generator-${var.environment}"
  description   = ""
  build_timeout = "5"
  service_role  = "arn:aws:iam::${var.accountId}:role/oap-codebuild-access-${var.environment}"

  source {
    type = "CODEPIPELINE"
  }

  logs_config {
    cloudwatch_logs {
      status = "ENABLED"
      group_name = aws_cloudwatch_log_group.oap_pdf_generator_logs.name  # Reference to the custom log group
    }
  }

  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
    type                        = "LINUX_CONTAINER"
    image_pull_credentials_type = "CODEBUILD"
    privileged_mode             = true

    environment_variable {
      name  = "stage"
      value = var.environment
    }
  }

  artifacts {
    type = "CODEPIPELINE"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}


