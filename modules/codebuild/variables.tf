variable "region" {
  description = "Aws region"
  type        = string

}
variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

variable "oap_frontend_project_source_type" {
  description = ""
  type        = string
}


variable "oap_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}


variable "oap_frontend_project_artifact_type" {
  description = ""
  type        = string
}