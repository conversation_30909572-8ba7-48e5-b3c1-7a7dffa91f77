provider "aws" {
  region = var.region
}
resource "aws_iam_role" "s3_access_role" {
  name = "s3AccessRole-${var.environment}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = [var.lambda_role_arn, "arn:aws:iam::${var.accountId}:root"]
        },
      },
    ],
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "s3_acess_policy" {
  name        = "s3AccessPolicy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "s3_cross_access_policy_attachment" {
  policy_arn = aws_iam_policy.s3_acess_policy.arn
  role       = aws_iam_role.s3_access_role.name
}
#codepipeline access role
resource "aws_iam_role" "codepipeline_access_role" {
  name = "oap-pipeline-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codepipeline.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

data "aws_iam_policy_document" "codepipeline_policy" {
  statement {
    effect = "Allow"

    actions = [
      "s3:GetObject",
      "s3:GetObjectVersion",
      "s3:GetBucketVersioning",
      "s3:PutObjectAcl",
      "s3:PutObject",
    ]

    resources = ["*"]
  }

  statement {
    effect    = "Allow"
    actions   = ["codestar-connections:UseConnection"]
    resources = ["*"]
  }

  statement {
    effect = "Allow"
    actions = [
      "codecommit:CancelUploadArchive",
      "codecommit:GetBranch",
      "codecommit:GetCommit",
      "codecommit:GetRepository",
      "codecommit:GetUploadArchiveStatus",
      "codecommit:UploadArchive"
    ]
    resources = [
      "arn:aws:codecommit:${var.region}:${var.accountId}:oap-backend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:oap-frontend",
      "arn:aws:codecommit:${var.region}:${var.accountId}:oap-pdf-generator",
      "arn:aws:codecommit:${var.region}:${var.accountId}:oap-handlers"
    ]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudwatch:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "codebuild:*"
    ]
    resources = ["*"]
  }

  statement {
    effect = "Allow"

    actions = [
      "cloudfront:GetDistribution",
      "cloudfront:GetInvalidation",
      "cloudfront:CreateInvalidation"
    ]
    resources = ["*"]
  }
}

resource "aws_iam_role_policy" "codepipeline_policy" {
  name   = "codepipeline_policy_${var.environment}"
  role   = aws_iam_role.codepipeline_access_role.id
  policy = data.aws_iam_policy_document.codepipeline_policy.json
}

# #codebuild access role
resource "aws_iam_role" "oap_codebuild_access" {
  name = "oap-codebuild-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "codebuild.amazonaws.com"
        }
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "oap_codebuild_access_policy" {
  name = "oap-codebuild-access-policy-${var.environment}"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "s3:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "athena:CreateWorkGroup"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "logs:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "lambda:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudfront:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "apigateway:*"
        ]
      },
      {
        Effect : "Allow",
        Resource : "*",
        Action : [
          "cloudwatch:*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "codebuild:CreateReportGroup",
          "codebuild:CreateReport",
          "codebuild:UpdateReport",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
          "codebuild:BatchGetProjects",
          "codebuild:CreateProject",
          "codebuild:ListProjects",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StopBuild",
          "codebuild:RetryBuild",
          "codebuild:UpdateProject"
        ],
        Resource : "*"
      },
      {
        Effect : "Allow",
        Action : [
          "dynamodb:*"
        ],
        Resource : [
          "arn:aws:dynamodb:${var.region}:${var.accountId}:table/*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "cloudformation:DescribeStacks",
          "cloudformation:DescribeStackResource",
          "cloudformation:ValidateTemplate",
          "cloudformation:DeleteChangeSet",
          "cloudformation:CreateChangeSet",
          "cloudformation:DescribeChangeSet",
          "cloudformation:ExecuteChangeSet",
          "cloudformation:DescribeStackEvents",
          "cloudformation:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "glue:CreateDatabase",
          "glue:BatchGetCrawlers",
          "glue:ListCrawlers",
          "glue:GetTables",
          "glue:GetDatabase",
          "glue:TagResource",
          "glue:UpdateDatabase",
          "glue:CreateTable",
          "glue:UpdateTable",
          "glue:GetTags",
          "glue:DeleteDatabase",
          "glue:DeleteTable",
          "glue:GetTable"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "iam:CreatePolicy",
          "iam:AttachRolePolicy",
          "iam:DeletePolicy",
          "iam:GetPolicy",
          "iam:ListPolicies",
          "iam:CreateRole",
          "iam:GetPolicy",
          "iam:GetRole",
          "iam:ListPolicies",
          "iam:ListRoles",
          "iam:UpdateRole",
          "iam:PutRolePolicy",
          "iam:GetRole",
          "iam:PassRole",
          "iam:ListRolePolicies",
          "iam:GetPolicyVersion",
          "iam:ListAttachedRolePolicies",
          "iam:ListPolicyVersions",
          "iam:ListInstanceProfilesForRole",
          "iam:DeleteRole",
          "iam:ListEntitiesForPolicy",
          "iam:GetRolePolicy",
          "iam:ListEntitiesForPolicy",
          "iam:DetachRolePolicy",
          "iam:DeleteRolePolicy"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "ecr:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "sns:*"
        ],
        Resource : [
          "*"
        ]
      },
       {
        Effect : "Allow",
        Action : [
          "ses:*"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect : "Allow",
        Action : [
          "events:PutRule",
          "events:DescribeRule",
          "events:ListTagsForResource",
          "events:DeleteRule",
          "events:PutTargets",
          "events:ListTargetsByRule",
          "events:RemoveTargets"
        ],
        Resource : [
          "*"
        ]
      },
      {
        Effect   = "Allow",
        Action   = ["codecommit:GitPull"],
        Resource = ["arn:aws:codecommit:eu-west-1:${var.accountId}:oap-handlers"],
      },
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "oap_codebuild_access_policy_attachment" {
  name       = "oap_codebuild_access_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.oap_codebuild_access_policy.arn
  roles      = [aws_iam_role.oap_codebuild_access.name]
}

# codepipeline start role
resource "aws_iam_role" "oap_codePipeline_start_access" {
  name = "oap-codePipeline-start-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Principal : {
          Service : "events.amazonaws.com"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "oap_codePipeline_start_policy" {
  name = "oap-codePipeline-start-access-policy-${var.environment}"

  policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Action : [
          "codepipeline:StartPipelineExecution"
        ],
        # Resource : [
        #   "arn:aws:codepipeline:${var.region}:${var.accountId}:oap-backend-service-${var.environment}"
        # ]
        Resource : [
          "*"
        ]
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "oap_codePipeline_start_access_policy_attachment" {
  name       = "oap_codePipeline_start_access_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.oap_codePipeline_start_policy.arn
  roles      = [aws_iam_role.oap_codePipeline_start_access.name]
}

#amplify create role
resource "aws_iam_role" "oap_amplify_start_access" {
  name = "oap-amplify-start-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version : "2012-10-17",
    Statement : [
      {
        Effect : "Allow",
        Principal : {
          Service : "amplify.amazonaws.com"
        },
        Action : "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}


resource "aws_iam_policy" "oap_amplify_start_policy" {
  name        = "oap-amplify-start-access-policy-${var.environment}"
  description = "Policy for starting Amplify deployments"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = ["amplify:StartDeployment"],
        Resource = ["*"], # Specify the resource ARN if you want to restrict to a specific Amplify app
      },
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:CreateLogGroup",
          "logs:DescribeLogGroups",
        ],
        Resource = [
          "arn:aws:logs:eu-west-1:${var.accountId}:log-group:/aws/amplify/*:log-stream:*",
          "arn:aws:logs:eu-west-1:${var.accountId}:log-group:/aws/amplify/*",
          "arn:aws:logs:eu-west-1:${var.accountId}:log-group:*",
        ],
      },
      {
        Effect   = "Allow",
        Action   = ["codecommit:GitPull"],
        Resource = ["arn:aws:codecommit:eu-west-1:${var.accountId}:oap-frontend"],
      },
    ],
  })

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}


resource "aws_iam_policy_attachment" "oap_amplify_start_access_policy_attachment" {
  name       = "oap_amplify_start_access_policy_attachment_${var.environment}"
  policy_arn = aws_iam_policy.oap_amplify_start_policy.arn
  roles      = [aws_iam_role.oap_amplify_start_access.name]
}

#lambda role
resource "aws_iam_role" "lambda_exec_role" {
  name = "oap-lambda-exec-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = "sts:AssumeRole"
        Effect = "Allow"
        Principal = {
          Service = "lambda.amazonaws.com"
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = var.s3_cross_account_role_arn
        }
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          Service = "ses.amazonaws.com"
        }
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "cognito-idp.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy" "lambda_exec_policy" {
  name        = "oap-lambda-exec-policy-${var.environment}"
  description = "gus lambda exec policy"

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogStreams"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "xray:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "amplify:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "apigateway:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "lambda:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "dynamodb:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Action = [
          "secretsmanager:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      },
      {
        Effect = "Allow"
        Action = [
          "sts:AssumeRole"
        ]
        Resource : "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ses:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "cognito-idp:*"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ssm:GetParameter",
          "ssm:GetParameters",
          "ssm:GetParametersByPath",
          "ssm:DescribeParameters"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "SNS:*",
          "sqs:*",
          "translate:*"
        ],
        Resource = "*"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "lambda_exec_policy_attachment" {
  name       = "oap-lambda-exec-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.lambda_exec_policy.arn
  roles      = [aws_iam_role.lambda_exec_role.name]
}

resource "aws_iam_role" "s3_cross_account_accessrole" {
  name = "s3OAPCrossAccountAccessRole-${var.environment}"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = [
            var.dev_oap_lambda_assumed_role_arn,
            var.prod_oap_lambda_assumed_role_arn,
            var.dev_oap_lambda_notification_arn, 
            var.prod_oap_lambda_notification_arn, 
            var.dev_user_access_arn, 
            var.prod_user_access_arn,
            var.dev_eip_integration_lambda_arn,
            var.prod_eip_integration_lambda_arn,
            "arn:aws:sts::${var.accountId}:assumed-role/gus-eip-integration-handlers-access-${var.environment}/gus-sf-hzu-educationcloud-integration-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/gus-eip-integration-handlers-access-${var.environment}/hzu-educationcloud-gus-sf-integration-${var.environment}",
            "arn:aws:iam::${var.accountId}:role/gus-lambda-exec-role-${var.environment}",
            "arn:aws:iam::${var.accountId}:role/lambda-exec-role-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/gus-lambda-exec-role-${var.environment}/gus-sf-hzu-educationcloud-integration-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/gus-lambda-exec-role-${var.environment}/hzu-educationcloud-gus-sf-integration-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/gus-lambda-exec-role-${var.environment}/gus-eip-services-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/lambda-exec-role-${var.environment}/gus-sf-hzu-educationcloud-integration-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/lambda-exec-role-${var.environment}/hzu-educationcloud-gus-sf-integration-${var.environment}",
            "arn:aws:sts::${var.accountId}:assumed-role/lambda-exec-role-${var.environment}/gus-eip-services-${var.environment}"
            ]
        },
      },
    ],
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "s3_cross_account_policy" {
  name        = "s3OAPCrossAccountAccessPolicy-${var.environment}"
  description = "A policy that allows access to s3"
  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Action = [
          "s3:*"
        ]
        Resource = "*"
        Effect   = "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_iam_policy_attachment" "s3_cross_account_access_policy_attachment" {
  name       = "oap-cross-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.s3_cross_account_policy.arn
  roles      = [aws_iam_role.s3_cross_account_accessrole.name]
}

resource "aws_iam_role" "lambda_authorizer_role" {
  name = "oap-lambda-authorizer-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "lambda.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      },
      {
        Effect = "Allow",
        Principal = {
          Service = "apigateway.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "lambda_invoke_policy" {
  name        = "oap-lambda-invoke-policy"
  description = "Policy to allow invoking a specific Lambda function"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "lambda:InvokeFunction",
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy_attachment" "lambda_authorizer_policy_attachment" {
  name       = "oap-lambda-authorizer-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.lambda_invoke_policy.arn
  roles      = [aws_iam_role.lambda_authorizer_role.name]
}

//codebuild policy

# Define the IAM policy
resource "aws_iam_policy" "codebuild_ses_access_policy" {
  name        = "codebuild-oap-infra-ses-access-policy-${var.environment}"
  policy      = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "ses:CreateConfigurationSet",
          "ses:DeleteConfigurationSet",
          "ses:DescribeConfigurationSet",
          "ses:CreateConfigurationSetEventDestination",
          "ses:DeleteConfigurationSetEventDestination",
          "ses:UpdateConfigurationSetEventDestination",
          "ses:GetConfigurationSetEventDestinations",
          "ses:ListConfigurationSets",
          "ses:VerifyDomainIdentity",
          "ses:VerifyDomainDkim",
          "ses:CreateEmailIdentity",
          "ses:DeleteEmailIdentity",
          "ses:GetEmailIdentity",
          "ses:PutEmailIdentityDkimAttributes",
          "ses:PutEmailIdentityMailFromAttributes",
          "ses:DeleteIdentity",
          "ses:DeleteIdentityPolicy",
          "ses:GetIdentityDkimAttributes",
          "ses:GetIdentityMailFromDomainAttributes",
          "ses:GetIdentityNotificationAttributes",
          "ses:GetIdentityPolicies",
          "ses:GetIdentityVerificationAttributes",
          "ses:ListIdentityPolicies",
          "ses:PutIdentityPolicy",
          "ses:SetIdentityDkimEnabled",
          "ses:SetIdentityMailFromDomain",
          "ses:SetIdentityNotificationTopic",
          "ses:VerifyDomainIdentity",
          "ses:VerifyEmailIdentity",
          "ses:CreateEmailIdentityPolicy",
          "ses:DeleteEmailIdentityPolicy",
          "ses:GetEmailIdentityPolicies",
          "ses:PutEmailIdentityConfigurationSetAttributes",
          "ses:PutEmailIdentityDkimSigningAttributes",
          "ses:ReplicateEmailIdentityDkimSigningKey",
          "ses:UpdateEmailIdentityPolicy",
          "ses:ListEmailIdentities",
          "ses:ListIdentities",
          "ses:GetIdentityVerificationAttributes",
          "ses:GetIdentityDkimAttributes",
          "ses:ListTagsForResource",
          "ses:GetConfigurationSet",
          "ses:PutConfigurationSetDeliveryOptions",
          "ses:PutConfigurationSetReputationOptions",
          "ses:PutConfigurationSetSendingOptions",
          "ses:PutConfigurationSetTrackingOptions",
          "ses:UpdateConfigurationSetEventDestination",
          "ses:CreateConfigurationSetTrackingOptions",
          "ses:DeleteConfigurationSetTrackingOptions",
          "ses:UpdateConfigurationSetSendingEnabled",
          "ses:UpdateConfigurationSetTrackingOptions",
          "ses:PutConfigurationSetSuppressionOptions",
          "ses:PutEmailIdentityConfigurationSetAttributes",
          "ses:TagResource",
          "ses:UntagResource"
        ]
        Resource = "*"
      }
    ]
  })
}


resource "aws_iam_role_policy_attachment" "codebuild_ses_access_attach_policy_to_role" {
  role       = "codebuild-oap-infra-${var.environment}-service-role" 
  policy_arn = aws_iam_policy.codebuild_ses_access_policy.arn
}

resource "aws_iam_policy" "codebuild_resource_access_policy" {
  name        = "codebuild-oap-infra-resource-access-policy-${var.environment}"
  policy      = jsonencode({
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "Statement1",
            "Effect": "Allow",
            "Action": [
                "cognito-identity:*",
                "cognito-idp:*",
                "ecs:*",
                "ecr:*",
                "codepipeline:*",
                "scheduler:*",
                "events:*",
                "iam:CreateUser",
                "iam:GetUser",
                "iam:ListGroupsForUser",
                "iam:CreateAccessKey",
                "iam:AttachUserPolicy",
                "iam:ListAccessKeys",
                "iam:ListAttachedUserPolicies",
                "iam:DetachUserPolicy",
                "iam:TagUser",
                "ec2:CreateVpc",
                "ec2:CreateTags",
                "ec2:DescribeVpcs",
                "ec2:DescribeVpcAttribute",
                "ec2:CreateSubnet",
                "ec2:DescribeSubnets",
                "ec2:CreateInternetGateway",
                "ec2:AttachInternetGateway",
                "ec2:DescribeInternetGateways",
                "ec2:CreateRouteTable",
                "ec2:DescribeRouteTables",
                "ec2:CreateRoute",
                "ec2:AssociateRouteTable",
                "ec2:DescribeAvailabilityZones",
                "ec2:DisassociateRouteTable",
                "ec2:DescribeNetworkInterfaces",
                "ec2:DeleteSubnet",
                "ec2:DeleteRouteTable",
                "ec2:ModifyVpcAttribute",
                "ec2:AllocateAddress",
                "ec2:DescribeAddresses",
                "ec2:ReleaseAddress",
                "ec2:CreateNatGateway",
                "ec2:DescribeNatGateways",
                "ec2:DeleteNatGateway",
                "ec2:CreateSecurityGroup",
                "ec2:DescribeSecurityGroups",
                "ec2:DeleteSecurityGroup",
                "ec2:RevokeSecurityGroupEgress",
                "ec2:AuthorizeSecurityGroupEgress",
                "ec2:DetachInternetGateway",
                "ec2:DeleteInternetGateway",
                "ec2:DescribeAddressesAttribute",
                "ec2:ReplaceRoute",
                "ec2:AuthorizeSecurityGroupIngress"
            ],
            "Resource": "*"
        },
        {
             "Effect": "Allow",
             "Action": [
                 "iam:CreatePolicy",
                 "iam:AttachRolePolicy",
                 "iam:DeletePolicy",
                 "iam:GetPolicy",
                 "iam:ListPolicies",
                 "iam:CreateRole",
                 "iam:GetPolicy",
                 "iam:GetRole",
                 "iam:ListPolicies",
                 "iam:ListRoles",
                 "iam:UpdateRole",
                 "iam:PutRolePolicy",
                 "iam:ListRolePolicies",
                 "iam:GetPolicyVersion",
                 "iam:ListAttachedRolePolicies",
                 "iam:ListPolicyVersions",
                 "iam:ListInstanceProfilesForRole",
                 "iam:DeleteRole",
                 "iam:ListEntitiesForPolicy",
                 "iam:GetRolePolicy",
                 "iam:ListEntitiesForPolicy",
                 "iam:DetachRolePolicy",
                 "iam:DeleteRolePolicy",
                 "iam:PassRole"
             ],
             "Resource": "arn:aws:iam::${var.accountId}:role/*"
         },
          {
             "Effect": "Allow",
             "Action": "route53:*",
             "Resource": "arn:aws:route53:::hostedzone/Z323BOK105S9QE"
	        }
    ]
})
}

resource "aws_iam_role_policy_attachment" "codebuild_resource_access_attach_policy_to_role" {
  role       = "codebuild-oap-infra-${var.environment}-service-role" 
  policy_arn = aws_iam_policy.codebuild_resource_access_policy.arn
}

resource "aws_lambda_permission" "on_cognito_presignup_permission" {
  statement_id  = "OnCognitoSignupPermission"
  action        = "lambda:InvokeFunction"
    function_name = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-preSignUpTrigger-${var.environment}"
  principal     = "*"
}

resource "aws_lambda_permission" "on_cognito_custom_email_permission" {
  statement_id  = "OnCognitoCustomTriggerPermission"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-customEmailTrigger-${var.environment}"
  principal     = "*"
}

resource "aws_lambda_permission" "on_cognito_pre_authentication" {
  statement_id  = "OnCognitoPreAuthenticationTrigger"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-preAuthenticationTrigger-${var.environment}"
  principal     = "*"
}

resource "aws_lambda_permission" "on_cognito_post_confirmation" {
  statement_id  = "OnCognitoPostConfirmationTrigger"
  action        = "lambda:InvokeFunction"
  function_name = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-postConfirmationTrigger-${var.environment}"
  principal     = "*"
}

resource "aws_iam_role" "s3_job_access_role" {
  name = "s3-job-access-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Service = "batchoperations.s3.amazonaws.com"
        },
        Action = "sts:AssumeRole"
      }
    ]
  })
}

resource "aws_iam_policy" "s3_job_access_policy" {
  name        = "s3-job-access-policy-${var.environment}"
  description = ""

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect   = "Allow",
        Action   = "lambda:*",
        Resource = "*"
      },
      {
        Effect   = "Allow",
        Action   = "s3:*",
        Resource = "*"
      }
    ]
  })
}

resource "aws_iam_policy_attachment" "s3_job_access_policy_attachment" {
  name       = "s3-job-access-policy-attachment-${var.environment}"
  policy_arn = aws_iam_policy.s3_job_access_policy.arn
  roles      = [aws_iam_role.s3_job_access_role.name]
}
