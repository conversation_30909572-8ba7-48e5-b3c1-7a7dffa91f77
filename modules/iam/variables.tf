variable "region" {
  description = "Aws region"
  type        = string

}
variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

variable "lambda_role_arn" {
  description = ""
  type        = string
}

variable "s3_cross_account_role_arn" {
  description = ""
  type        = string
}

variable "prod_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}

variable "dev_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}
variable "dev_oap_lambda_notification_arn" {
  description = ""
  type        = string
}

variable "dev_eip_integration_lambda_arn" {
  description = ""
  type        = string
}

variable "prod_eip_integration_lambda_arn" {
  description = ""
  type        = string
}

variable "prod_oap_lambda_notification_arn" {
  description = ""
  type        = string
}

variable "dev_user_access_arn"{
  description = ""
  type = string
}

variable "prod_user_access_arn"{
  description = ""
  type = string
}