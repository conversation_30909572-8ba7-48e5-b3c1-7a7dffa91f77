provider "aws" {
  region = var.region
}

# Create ecs cluster
resource "aws_ecs_cluster" "ibd_cluster" {
  name = "lim-oap-${var.environment}"
}

# Create ecr repository
resource "aws_ecr_repository" "lim_oap" {
  name = "lim-oap-${var.environment}"

  tags = {
    Environment = "Production"
    Owner       = "YourName"
  }
}

resource "aws_ecr_lifecycle_policy" "lim_oap_lifecycle_policy" {
  repository = aws_ecr_repository.lim_oap.name

  policy = jsonencode({
    rules = [
      {
        rulePriority    = 1,
        description     = "in prod 10 images and in dev 3 images ",
        selection       = {
          tagStatus = "any",
          countType = "imageCountMoreThan",
          countNumber = var.environment == "prod" ? 10 : 3  # Adjust retention period as needed
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}


# Use local variables to reference the repository URI
locals {
  ecr_repo_uri = aws_ecr_repository.lim_oap.repository_url
  image_with_tag = "${local.ecr_repo_uri}:latest"
}

# IAM Role for ECS Task
resource "aws_iam_role" "scheduler" {
  name = "lim-cron-scheduler-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = {
          Service = ["ecs-tasks.amazonaws.com", "scheduler.amazonaws.com"]
        }
        Action    = "sts:AssumeRole"
      },
      {
        Action = "sts:AssumeRole",
        Effect = "Allow",
        Principal = {
          AWS = var.s3_role_arn
        }
      },
          ]
  })
}

# IAM Policy for ECS Task
resource "aws_iam_policy" "scheduler" {
  name = "cron-scheduler-policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        # allow scheduler to execute the task
        Effect = "Allow",
        Action = [
          "ecs:RunTask",
          "ecr:GetAuthorizationToken"
        ]
        # trim :<revision> from arn, to point at the whole task definition and not just one revision
        Resource = ["*"]
      },
      {
        Effect = "Allow",
        Action = [
          "s3:PutObject"
        ],
        Resource = [
          "arn:aws:s3:::lim-application-exports-${var.environment}/*"
          ]
      },
      {
        # allow scheduler to execute the task
        Effect = "Allow",
        Action = [
          "ecs:DescribeTasks",
          "ecs:StopTask",
          "ecs:DescribeTaskDefinition",
          "ecs:ListTasks"
        ],
        Resource = [
          "arn:aws:iam::${var.accountId}:role/lim-cron-scheduler-role-${var.environment}"
          ]
      },
      {
        Effect = "Allow",
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:GetDownloadUrlForLayer",
          "ecr:BatchGetImage"
        ],
        Resource = [
          "arn:aws:ecr:${var.region}:${var.accountId}:repository/lim-oap-${var.environment}"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents",
          "logs:DescribeLogGroups",
          "logs:DescribeLogStreams",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = [
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:lim-cron-scheduler-role-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:kyb-kyc-logger-${var.environment}:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:lim-oap-exporter-${var.environment}:log-stream:*",
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:lim-oap-csv-exporter-kpis-${var.environment}:log-stream:*"
        ]
      },
      #{ # allow scheduler to set the IAM roles of your task
      #  Effect = "Allow",
      #  Action = [
      #    "iam:PassRole",
      #    "events:*",
      #    "ecr:*",
      #    "logs:*",
      #    "ecs:*",
      #    "ec2:*",
      #    "dynamodb:Query",
      #    "s3:PutObject"
      #  ]
      #  Resource = ["*"]
      #},
{
        Effect = "Allow",
        Action = ["secretsmanager:GetSecretValue"]
        Resource = ["arn:aws:secretsmanager:${var.region}:${var.accountId}:secret:lim-oap-${var.environment}"]
      },
    ]
  })
}

# get the aws secret
data "aws_secretsmanager_secret" "lim_oap" {
  name = "lim-oap-${var.environment}"
}

data "aws_secretsmanager_secret_version" "my_secret_version" {
  secret_id = data.aws_secretsmanager_secret.lim_oap.id
}
locals {
  my_secret_value = jsondecode(data.aws_secretsmanager_secret_version.my_secret_version.secret_string)
}

resource "aws_ecs_task_definition" "task" {
  requires_compatibilities = ["FARGATE"]
  cpu                      = 256
  memory                   = 512
  skip_destroy             = true
  network_mode             = "awsvpc"
  # role that allows ECS to spin up your task, for example needs permission to ECR to get container image
  execution_role_arn = aws_iam_role.scheduler.arn
  # role that your workload gets to access AWS APIs
  task_role_arn      = aws_iam_role.scheduler.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64"
  }

  family = "lim-oap-${var.environment}"
  container_definitions = jsonencode([
    {
      name         = "lim-oap-${var.environment}"
      image        = local.image_with_tag 
      cpu          = 256
      memory       = 512
      essential    = true
      requires_compatibilities = ["FARGATE"]
      network_mode             = "awsvpc"
      portMappings = []
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.log_group.name
          awslogs-region        = "${var.region}"
          awslogs-stream-prefix = "lim-oap"
        }
      }
      environment = [
        {
          name  = "S3_ACCESS_KEY_ID"
          value = local.my_secret_value["s3_access_key_id"]
        },
        {
          name  = "S3_SECRET_ACCESS_KEY"
          value = local.my_secret_value["s3_secret_access_key"]
        }
      ]
    }
  ])
}

resource "aws_cloudwatch_log_group" "log_group" {
  name              = "lim-oap-exporter-${var.environment}"  # Name of the log group
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed  

}
# ECS Scheduled Task Rule
resource "aws_scheduler_schedule" "cron" {
  name        = "lim-oap-${var.environment}"
  group_name  = "default"

  flexible_time_window {
    mode = "OFF"
  }

  schedule_expression = "cron(0 2 * * ? *)" # run's once a day 7:30 AM IST 

  target {
    arn      = aws_ecs_cluster.ibd_cluster.arn # arn of the ecs cluster to run on
    # role that allows scheduler to start the task (explained later)
    role_arn = aws_iam_role.scheduler.arn

    ecs_parameters {
      # trimming the revision suffix here so that schedule always uses latest revision
      task_definition_arn = trimsuffix(aws_ecs_task_definition.task.arn, ":${aws_ecs_task_definition.task.revision}")
      launch_type         = "FARGATE"
      network_configuration {
        assign_public_ip = false
        #subnets = module.vpc.subnet_private
        #security_groups =  module.vpc.security_group
        subnets = var.private_subnet_id[var.environment]
        security_groups = var.security_group[var.environment]
    }
  }
}
}

resource "aws_iam_role_policy_attachment" "scheduler" {
  policy_arn = aws_iam_policy.scheduler.arn
  role       = aws_iam_role.scheduler.name
}

resource "aws_s3_bucket" "lim_oap_artifact" {
  bucket = "lim-oap-pipeline-${var.environment}"

  tags = {
    Name        = "lim-oap-pipeline-${var.environment}"
    Environment = "${var.environment}"
  }
}

resource "aws_codepipeline" "lim_oap_pipeline" {
  name     = "lim-oap-csv-exporter-${var.environment}"
  role_arn = aws_iam_role.pipeline_role.arn

  artifact_store {
    location = "lim-oap-pipeline-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        RepositoryName = "lim-oap-csv-exporter"
        BranchName     = "${var.environment}"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name            = "BuildAction"
      category        = "Build"
      owner           = "AWS"
      provider        = "CodeBuild"
      version         = "1"
      input_artifacts = ["source_output"]
      output_artifacts = ["build_output"]

      configuration = {
        ProjectName = "lim-oap-${var.environment}"
      }
    }
  }
}

resource "aws_cloudwatch_event_rule" "lim_oap_csv_exporter_codecommit_trigger_rule" {
  name = "lim-oap-csv-exporter-CodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:lim-oap-csv-exporter"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "oap_backend_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.lim_oap_csv_exporter_codecommit_trigger_rule.name
  #target_id = "oapBackendCodecommitEventTarget"
  arn       = aws_codepipeline.lim_oap_pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/oap-codePipeline-start-access-${var.environment}"
}

resource "aws_iam_role" "pipeline_role" {
  name = "lim-oap-pipeline-role-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "code_pipeline" {
  name        = "lim-oap-codepipeline-policy-${var.environment}"
  description = "An lim oap IAM policy"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Action = [
          "codecommit:CancelUploadArchive",
          "codecommit:GetBranch",
          "codecommit:GetCommit",
          "codecommit:GetRepository",
          "codecommit:GetUploadArchiveStatus",
          "codecommit:UploadArchive",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds",
          "s3:GetObject",
          "s3:PutObject",
          "s3:PutBucketPolicy",
          "s3:ListBucket",
          "s3:CreateBucket",
        ]
        Resource = [
          "arn:aws:codebuild:${var.region}:${var.accountId}:project/lim-oap-${var.environment}",
          "arn:aws:codecommit:${var.region}:${var.accountId}:lim-oap-csv-exporter",
          "arn:aws:s3:::lim-oap-pipeline-${var.environment}/*"
        ]
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "pipeline_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = aws_iam_policy.code_pipeline.arn
}

resource "aws_iam_role_policy_attachment" "pipeline_policy_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess"
}

resource "aws_iam_role" "lim_oap_codebuild_role" {
  name = "lim-oap-codebuild-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "lim_oap_codebuild_policy" {
  name        = "lim-oap-codebuild-policy-${var.environment}"
  description = "IAM policy for CodeBuild project"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Action = [
          "logs:CreateLogGroup",
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = [ 
          "arn:aws:logs:${var.region}:${var.accountId}:log-group:/aws/codebuild/lim-oap-${var.environment}:*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "ecr:GetAuthorizationToken"
        ],
        Resource = "*"
      },
      {
        Effect = "Allow",
        Action = [
          "ecr:BatchCheckLayerAvailability",
          "ecr:BatchGetImage",
          "ecr:PutImage",
          "ecr:InitiateLayerUpload",
          "ecr:UploadLayerPart",
          "ecr:CompleteLayerUpload"
        ],
        Resource = "arn:aws:ecr:eu-west-1:${var.accountId}:repository/lim-oap-${var.environment}"
      },
      {
        Effect = "Allow",
        Action = [
          "s3:GetObject",
          "s3:ListBucket",
          "s3:CreateBucket",
          "s3:PutBucketPolicy",
          "s3:PutObject"
        ],
        Resource = [
          "arn:aws:s3:::lim-oap-pipeline-${var.environment}",
          "arn:aws:s3:::lim-oap-pipeline-${var.environment}/*"
        ]
      },
      {
        Effect = "Allow",
        Action = [
          "codebuild:CreateReportGroup",
          "codebuild:CreateReport",
          "codebuild:UpdateReport",
          "codebuild:BatchPutTestCases",
          "codebuild:BatchPutCodeCoverages",
          "codebuild:StartBuild",
          "codebuild:BatchGetBuilds"
        ],
        Resource = "arn:aws:codebuild:eu-west-1:${var.accountId}:project/lim-oap-${var.environment}"
      }
    ]
  })
}

resource "aws_iam_role_policy_attachment" "codebuild_policy_attachment" {
  role       = aws_iam_role.lim_oap_codebuild_role.name
  policy_arn = aws_iam_policy.lim_oap_codebuild_policy.arn
}

resource "aws_codebuild_project" "lim_oap" {
  name = "lim-oap-${var.environment}"
  description = "lim-oap CodeBuild project created with Terraform"
  service_role  = aws_iam_role.lim_oap_codebuild_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  # Source configuration
  source {
    type            = "CODEPIPELINE"  # Use CodePipeline as source type
    # Define the buildspec content directly within Terraform
    buildspec = <<EOF
version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      # Install Node.js dependencies using npm or yarn
      - npm install -g pnpm
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ${var.accountId}.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      - REPOSITORY_URI=${var.accountId}.dkr.ecr.${var.region}.amazonaws.com/lim-oap-${var.environment}
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=build-$(echo $CODEBUILD_BUILD_ID | awk -F":" '{print $2}')
  build:
    commands:
      #- echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg NODE_ENV=${var.environment} -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"lim-oap-${var.environment}","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
      - cat imagedefinitions.json 
artifacts:
    files: imagedefinitions.json
EOF
  }

  # Environment configuration
  environment {
    compute_type                = "BUILD_GENERAL1_SMALL" # Set compute type
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0" # Set Docker image for environment
    type                        = "LINUX_CONTAINER" # Set environment type
    image_pull_credentials_type = "CODEBUILD" # Specify how CodeBuild obtains image pull credentials
  }
}

output "cluster_name" {
  value = aws_ecs_cluster.ibd_cluster.name
}

output "cluster_arn" {
  value = aws_ecs_cluster.ibd_cluster.arn
}

output "task_defination" {
  value =aws_ecs_task_definition.task.arn  
}

output "image_tag" {
  value = local.image_with_tag
}
