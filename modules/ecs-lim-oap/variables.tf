variable "region" {
  description = "Choose your region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Choose your region"
  type        = string
}
variable "s3_role_arn" {
  description = "s3 role arn"
  type        = string
}

variable "private_subnet_id" {
  type = map
  description = "Map of subnets for each environment"
  default = {
    dev = ["subnet-0a13cacdf1be3c00c", "subnet-0017a5a0c7217a524"]
    prod  = ["subnet-00b038041f6daa6a1", "subnet-0679057c5c7758a30"]
  }
}

variable "security_group" {
  type = map
  description = "Map of security groups for each environment"
  default = {
    dev   = ["sg-0915895f761892085" ]
    prod  = ["sg-093e9a0cf4446bc6c"]
  }
}
