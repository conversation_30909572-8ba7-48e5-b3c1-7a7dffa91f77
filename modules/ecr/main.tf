
//ECR lifecycle policy for oap-backend-dev
resource "aws_ecr_lifecycle_policy" "serverless_oap_backend_lifecycle_policy" {
  repository = "serverless-oap-backend-service-${var.environment}"
 
  policy = jsonencode({
    rules = [
      {
        rulePriority = 1,
        description  = "in prod 10 images and in dev 3 images",
        selection = {
          tagStatus   = "any",
          countType   = "imageCountMoreThan",
          countNumber = "${var.environment}" == "prod" ? 10 : 3
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}