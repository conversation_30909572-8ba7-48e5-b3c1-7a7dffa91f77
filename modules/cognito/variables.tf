variable "region" {
  description = "Aws region"
  type        = string
}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

variable "accountId" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_id" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_secret" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_oidc_issuer" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_authorize_scopes" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_attributes_request_method" {
  description = ""
  type        = string
}

variable "hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_callback_urls" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_logout_urls" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = ""
  type        = list(string)
}

variable "ses_mailer_arn" {
  description = ""
  type        = string
}

variable "ucw_ses_mailer_arn"{
  description = ""
  type        = string
}

variable "ueg_ses_mailer_arn"{
  description = ""
  type        = string
}

variable "hzu" {
  description = ""
  type        = string
}

variable "user_type_student" {
  description = ""
  type        = string
}

# LIM
variable "lim_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for LIM student Cognito user pool client"
  type        = list(string)
}

variable "lim" {
  description = ""
  type        = string
}

# UCW - student
variable "ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw" {
  description = ""
  type        = string
}

# UNFC - student
variable "unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc" {
  description = ""
  type        = string
}

//UE
variable "ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg" {
  description = ""
  type        = string
}
