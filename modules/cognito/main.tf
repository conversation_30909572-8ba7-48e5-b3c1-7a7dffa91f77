provider "aws" {
  region = var.region
}

# HERZING

resource "aws_cognito_user_pool" "hzu_student_oap_user_pool" {
  name                = "${var.environment}-${var.hzu}-${var.user_type_student}-oap"
  username_attributes = ["email"]
  email_configuration {
    source_arn            = var.ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    required            = true
    mutable             = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name                = "phoneNumber"
    attribute_data_type = "String"
    required            = false
    mutable             = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name                = "userId"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name                = "firstName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "lastName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "country"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_uppercase = true
    require_symbols   = true
  }
  auto_verified_attributes = ["email"]
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-customEmailTrigger-${var.environment}"
    # post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:${var.hzu}-${var.user_type_student}-oap-postSignUpTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS Cognito User Pool Identity Provider
# resource "aws_cognito_identity_provider" "hzu_student_oap_user_pool_identity_provider" {
#   provider_name = "linkedin"
#   provider_type = "OIDC"
#   user_pool_id  = aws_cognito_user_pool.hzu_student_oap_user_pool.id

#   provider_details = {
#     client_id                 = var.cognito_linkedin_identity_provider_client_id
#     client_secret             = var.cognito_linkedin_identity_provider_client_secret
#     authorize_scopes          = var.cognito_linkedin_identity_provider_authorize_scopes
#     oidc_issuer               = var.cognito_linkedin_identity_provider_oidc_issuer
#     attributes_request_method = var.cognito_linkedin_identity_provider_attributes_request_method
#     authorize_url             = "https://www.linkedin.com/oauth/v2/authorization"
#     token_url                 = "https://www.linkedin.com/oauth/v2/accessToken"
#     attributes_url            = "https://api.linkedin.com/v2/userinfo"
#     jwks_uri                  = "https://www.linkedin.com/oauth/openid/jwks"
#   }

#   attribute_mapping = {
#     "custom:firstName" = "given_name"
#     "custom:lastName"  = "family_name"
#     "email"            = "email"
#     "email_verified"   = "email_verified"
#     "picture"          = "picture"
#     "username"         = "sub"
#   }
# }

# Define the AWS Cognito User Pool Client
resource "aws_cognito_user_pool_client" "hzu_student_oap_user_client" {
  name                 = "${var.environment}-${var.hzu}-${var.user_type_student}-oap-client"
  user_pool_id         = aws_cognito_user_pool.hzu_student_oap_user_pool.id
  generate_secret      = false
  allowed_oauth_flows  = var.hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.hzu_student_oap_cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.hzu_student_oap_cognito_user_pool_client_callback_urls
  logout_urls   = var.hzu_student_oap_cognito_user_pool_client_logout_urls

  supported_identity_providers = var.hzu_student_oap_cognito_user_pool_client_supported_identity_providers

  read_attributes  = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]
  write_attributes = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]

  access_token_validity  = 2
  id_token_validity      = 2
  refresh_token_validity = 4
}

# Define the AWS Cognito Identity Pool
resource "aws_cognito_identity_pool" "hzu_student_oap_identity_pool" {
  identity_pool_name               = "${var.environment}-${var.hzu}-${var.user_type_student}-oap-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.hzu_student_oap_user_client.id
    provider_name = aws_cognito_user_pool.hzu_student_oap_user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "hzu_student_oap_authenticated_role" {
  name = "${var.environment}-cognito-${var.hzu}-${var.user_type_student}-oap-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.hzu_student_oap_identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "hzu_student_oap_authenticated_role_policy" {
  name        = "${var.hzu}-${var.user_type_student}-oap-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action" : "*",
        "Resource" : "*",
        "Effect" : "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "hzu_student_oap_authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.hzu_student_oap_authenticated_role_policy.arn
  role       = aws_iam_role.hzu_student_oap_authenticated_role.name
}
# Attach the IAM Role to the Cognito Identity Pool
resource "aws_cognito_identity_pool_roles_attachment" "hzu_student_oap_identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.hzu_student_oap_identity_pool.id
  roles = {
    authenticated = aws_iam_role.hzu_student_oap_authenticated_role.arn
  }
}

# Define the Cognito User Pool Domain (naming need to discuss)
resource "aws_cognito_user_pool_domain" "hzu_student_oap_user_pool_domain" {
  domain = "${var.hzu}-${var.user_type_student}-oap-${var.environment}"
  user_pool_id = aws_cognito_user_pool.hzu_student_oap_user_pool.id
}

# LIM

resource "aws_cognito_user_pool" "lim_student_oap_user_pool" {
  name                = "${var.environment}-${var.lim}-${var.user_type_student}-oap"
  username_attributes = ["email"]
  email_configuration {
    source_arn            = var.ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    required            = true
    mutable             = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name                = "phoneNumber"
    attribute_data_type = "String"
    required            = false
    mutable             = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name                = "userId"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name                = "firstName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "lastName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "country"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_uppercase = true
    require_symbols   = true
  }
  auto_verified_attributes = ["email"]
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-customEmailTrigger-${var.environment}"
    # post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:${var.lim}-${var.user_type_student}-oap-postSignUpTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS Cognito User Pool Client
resource "aws_cognito_user_pool_client" "lim_student_oap_user_client" {
  name                 = "${var.environment}-${var.lim}-${var.user_type_student}-oap-client"
  user_pool_id         = aws_cognito_user_pool.lim_student_oap_user_pool.id
  generate_secret      = false
  allowed_oauth_flows  = var.lim_student_oap_cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.lim_student_oap_cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.lim_student_oap_cognito_user_pool_client_callback_urls
  logout_urls   = var.lim_student_oap_cognito_user_pool_client_logout_urls

  supported_identity_providers = var.lim_student_oap_cognito_user_pool_client_supported_identity_providers

  read_attributes  = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]
  write_attributes = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]

  access_token_validity  = 2
  id_token_validity      = 2
  refresh_token_validity = 4
}

# Define the AWS Cognito Identity Pool
resource "aws_cognito_identity_pool" "lim_student_oap_identity_pool" {
  identity_pool_name               = "${var.environment}-${var.lim}-${var.user_type_student}-oap-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.lim_student_oap_user_client.id
    provider_name = aws_cognito_user_pool.lim_student_oap_user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "lim_student_oap_authenticated_role" {
  name = "${var.environment}-cognito-${var.lim}-${var.user_type_student}-oap-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.lim_student_oap_identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "lim_student_oap_authenticated_role_policy" {
  name        = "${var.lim}-${var.user_type_student}-oap-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action" : "*",
        "Resource" : "*",
        "Effect" : "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "lim_student_oap_authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.lim_student_oap_authenticated_role_policy.arn
  role       = aws_iam_role.lim_student_oap_authenticated_role.name
}
# Attach the IAM Role to the Cognito Identity Pool
resource "aws_cognito_identity_pool_roles_attachment" "lim_student_oap_identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.lim_student_oap_identity_pool.id
  roles = {
    authenticated = aws_iam_role.lim_student_oap_authenticated_role.arn
  }
}

# Define the Cognito User Pool Domain (naming need to discuss)
resource "aws_cognito_user_pool_domain" "lim_student_oap_user_pool_domain" {
  domain = "${var.lim}-${var.user_type_student}-oap-${var.environment}"
  user_pool_id = aws_cognito_user_pool.lim_student_oap_user_pool.id
}



# UCW - student

resource "aws_cognito_user_pool" "ucw_student_oap_user_pool" {
  name                = "${var.environment}-${var.ucw}-${var.user_type_student}-oap"
  username_attributes = ["email"]
  email_configuration {
    source_arn            = var.ucw_ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    required            = true
    mutable             = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name                = "phoneNumber"
    attribute_data_type = "String"
    required            = false
    mutable             = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name                = "userId"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name                = "firstName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "lastName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "country"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_uppercase = true
    require_symbols   = true
  }
  auto_verified_attributes = ["email"]
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-customEmailTrigger-${var.environment}"
    # post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:${var.ucw}-${var.user_type_student}-oap-postSignUpTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS Cognito User Pool Client
resource "aws_cognito_user_pool_client" "ucw_student_oap_user_client" {
  name                 = "${var.environment}-${var.ucw}-${var.user_type_student}-oap-client"
  user_pool_id         = aws_cognito_user_pool.ucw_student_oap_user_pool.id
  generate_secret      = false
  allowed_oauth_flows  = var.ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.ucw_student_oap_cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.ucw_student_oap_cognito_user_pool_client_callback_urls
  logout_urls   = var.ucw_student_oap_cognito_user_pool_client_logout_urls

  supported_identity_providers = var.ucw_student_oap_cognito_user_pool_client_supported_identity_providers

  read_attributes  = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]
  write_attributes = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]

  access_token_validity  = 2
  id_token_validity      = 2
  refresh_token_validity = 4
}

# Define the AWS Cognito Identity Pool
resource "aws_cognito_identity_pool" "ucw_student_oap_identity_pool" {
  identity_pool_name               = "${var.environment}-${var.ucw}-${var.user_type_student}-oap-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.ucw_student_oap_user_client.id
    provider_name = aws_cognito_user_pool.ucw_student_oap_user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "ucw_student_oap_authenticated_role" {
  name = "${var.environment}-cognito-${var.ucw}-${var.user_type_student}-oap-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.ucw_student_oap_identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "ucw_student_oap_authenticated_role_policy" {
  name        = "${var.ucw}-${var.user_type_student}-oap-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action" : "*",
        "Resource" : "*",
        "Effect" : "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "ucw_student_oap_authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.ucw_student_oap_authenticated_role_policy.arn
  role       = aws_iam_role.ucw_student_oap_authenticated_role.name
}
# Attach the IAM Role to the Cognito Identity Pool
resource "aws_cognito_identity_pool_roles_attachment" "ucw_student_oap_identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.ucw_student_oap_identity_pool.id
  roles = {
    authenticated = aws_iam_role.ucw_student_oap_authenticated_role.arn
  }
}

# Define the Cognito User Pool Domain (naming need to discuss)
resource "aws_cognito_user_pool_domain" "ucw_student_oap_user_pool_domain" {
  domain = "${var.ucw}-${var.user_type_student}-oap-${var.environment}"
  user_pool_id = aws_cognito_user_pool.ucw_student_oap_user_pool.id
}

# UNFC - student

resource "aws_cognito_user_pool" "unfc_student_oap_user_pool" {
  name                = "${var.environment}-${var.unfc}-${var.user_type_student}-oap"
  username_attributes = ["email"]
  email_configuration {
    source_arn            = var.ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    required            = true
    mutable             = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name                = "phoneNumber"
    attribute_data_type = "String"
    required            = false
    mutable             = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name                = "userId"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name                = "firstName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "lastName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "country"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_uppercase = true
    require_symbols   = true
  }
  auto_verified_attributes = ["email"]
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-customEmailTrigger-${var.environment}"
    # post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:${var.unfc}-${var.user_type_student}-oap-postSignUpTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS Cognito User Pool Client
resource "aws_cognito_user_pool_client" "unfc_student_oap_user_client" {
  name                 = "${var.environment}-${var.unfc}-${var.user_type_student}-oap-client"
  user_pool_id         = aws_cognito_user_pool.unfc_student_oap_user_pool.id
  generate_secret      = false
  allowed_oauth_flows  = var.unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.unfc_student_oap_cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.unfc_student_oap_cognito_user_pool_client_callback_urls
  logout_urls   = var.unfc_student_oap_cognito_user_pool_client_logout_urls

  supported_identity_providers = var.unfc_student_oap_cognito_user_pool_client_supported_identity_providers

  read_attributes  = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]
  write_attributes = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]

  access_token_validity  = 2
  id_token_validity      = 2
  refresh_token_validity = 4
}

# Define the AWS Cognito Identity Pool
resource "aws_cognito_identity_pool" "unfc_student_oap_identity_pool" {
  identity_pool_name               = "${var.environment}-${var.unfc}-${var.user_type_student}-oap-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.unfc_student_oap_user_client.id
    provider_name = aws_cognito_user_pool.unfc_student_oap_user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "unfc_student_oap_authenticated_role" {
  name = "${var.environment}-cognito-${var.unfc}-${var.user_type_student}-oap-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.unfc_student_oap_identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "unfc_student_oap_authenticated_role_policy" {
  name        = "${var.unfc}-${var.user_type_student}-oap-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action" : "*",
        "Resource" : "*",
        "Effect" : "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "unfc_student_oap_authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.unfc_student_oap_authenticated_role_policy.arn
  role       = aws_iam_role.unfc_student_oap_authenticated_role.name
}
# Attach the IAM Role to the Cognito Identity Pool
resource "aws_cognito_identity_pool_roles_attachment" "unfc_student_oap_identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.unfc_student_oap_identity_pool.id
  roles = {
    authenticated = aws_iam_role.unfc_student_oap_authenticated_role.arn
  }
}

# Define the Cognito User Pool Domain (naming need to discuss)
resource "aws_cognito_user_pool_domain" "unfc_student_oap_user_pool_domain" {
  domain = "${var.unfc}-${var.user_type_student}-oap-${var.environment}"
  user_pool_id = aws_cognito_user_pool.unfc_student_oap_user_pool.id
}

# UEG - student

resource "aws_cognito_user_pool" "ueg_student_oap_user_pool" {
  name                = "${var.environment}-${var.ueg}-${var.user_type_student}-oap"
  username_attributes = ["email"]
  email_configuration {
    source_arn            = var.ueg_ses_mailer_arn
    email_sending_account = "DEVELOPER"
  }

  verification_message_template {
    default_email_option = "CONFIRM_WITH_CODE"
  }

  schema {
    name                = "email"
    attribute_data_type = "String"
    required            = true
    mutable             = true
    string_attribute_constraints {
      min_length = 8
      max_length = 128
    }
  }

  schema {
    name                = "phoneNumber"
    attribute_data_type = "String"
    required            = false
    mutable             = true
    string_attribute_constraints {
      min_length = 10
      max_length = 15
    }
  }

  schema {
    name                = "userId"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 6
      max_length = 32
    }
  }

  schema {
    name                = "firstName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "lastName"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  schema {
    name                = "country"
    attribute_data_type = "String"
    mutable             = true
    string_attribute_constraints {
      min_length = 1
      max_length = 50
    }
  }

  password_policy {
    minimum_length    = 8
    require_lowercase = true
    require_numbers   = true
    require_uppercase = true
    require_symbols   = true
  }
  auto_verified_attributes = ["email"]
  lambda_config {
    pre_sign_up  = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-preSignUpTrigger-${var.environment}"
    custom_message = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-${var.user_type_student}-oap-customEmailTrigger-${var.environment}"
    pre_authentication = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-preAuthenticationTrigger-${var.environment}"
    post_confirmation = "arn:aws:lambda:${var.region}:${var.accountId}:function:gus-student-oap-postConfirmationTrigger-${var.environment}"
  }

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cognito_user_pool_client" "ueg_student_oap_user_client" {
  name                 = "${var.environment}-${var.ueg}-${var.user_type_student}-oap-client"
  user_pool_id         = aws_cognito_user_pool.ueg_student_oap_user_pool.id
  generate_secret      = false
  allowed_oauth_flows  = var.ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows
  allowed_oauth_scopes = var.ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes

  explicit_auth_flows = var.ueg_student_oap_cognito_user_pool_client_explicit_auth_flows

  callback_urls = var.ueg_student_oap_cognito_user_pool_client_callback_urls
  logout_urls   = var.ueg_student_oap_cognito_user_pool_client_logout_urls

  supported_identity_providers = var.ueg_student_oap_cognito_user_pool_client_supported_identity_providers

  read_attributes  = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]
  write_attributes = ["email", "custom:phoneNumber", "custom:firstName", "custom:lastName", "custom:country", "custom:userId"]

  access_token_validity  = 2
  id_token_validity      = 2
  refresh_token_validity = 4
}

resource "aws_cognito_identity_pool" "ueg_student_oap_identity_pool" {
  identity_pool_name               = "${var.environment}-${var.ueg}-${var.user_type_student}-oap-identityPool"
  allow_unauthenticated_identities = false
  cognito_identity_providers {
    client_id     = aws_cognito_user_pool_client.ueg_student_oap_user_client.id
    provider_name = aws_cognito_user_pool.ueg_student_oap_user_pool.endpoint
  }
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Define the AWS IAM Role for Cognito Identity Pool Auth
resource "aws_iam_role" "ueg_student_oap_authenticated_role" {
  name = "${var.environment}-cognito-${var.ueg}-${var.user_type_student}-oap-identitypoolAuth-role"
  assume_role_policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Effect = "Allow",
        Principal = {
          Federated = "cognito-identity.amazonaws.com"
        },
        Action = "sts:AssumeRoleWithWebIdentity",
        Condition = {
          StringEquals = {
            "cognito-identity.amazonaws.com:aud" = aws_cognito_identity_pool.ueg_student_oap_identity_pool.id
          },
          "ForAnyValue:StringLike" = {
            "cognito-identity.amazonaws.com:amr" = "authenticated"
          }
        }
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_policy" "ueg_student_oap_authenticated_role_policy" {
  name        = "${var.ueg}-${var.user_type_student}-oap-authenticated-role-policy-${var.environment}"
  description = "A policy that allows access to s3"

  policy = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        "Action" : "*",
        "Resource" : "*",
        "Effect" : "Allow"
      }
    ]
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_iam_role_policy_attachment" "ueg_student_oap_authenticated_policy_attachment" {
  policy_arn = aws_iam_policy.ueg_student_oap_authenticated_role_policy.arn
  role       = aws_iam_role.ueg_student_oap_authenticated_role.name
}

resource "aws_cognito_identity_pool_roles_attachment" "ueg_student_oap_identity_pool_role_attachment" {
  identity_pool_id = aws_cognito_identity_pool.ueg_student_oap_identity_pool.id
  roles = {
    authenticated = aws_iam_role.ueg_student_oap_authenticated_role.arn
  }
}

resource "aws_cognito_user_pool_domain" "ueg_student_oap_user_pool_domain" {
  domain = "${var.ueg}-${var.user_type_student}-oap-${var.environment}"
  user_pool_id = aws_cognito_user_pool.ueg_student_oap_user_pool.id
}
