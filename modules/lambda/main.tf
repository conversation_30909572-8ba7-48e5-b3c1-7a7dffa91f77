
data "archive_file" "lambda_zip" {
  type        = "zip"
  source_dir  = "${path.module}/code"
  output_path = "${path.module}/code-${replace(timestamp(), "/[^0-9]/", "")}.zip"
}

resource "aws_lambda_function" "amplify_app" {
  filename      = data.archive_file.lambda_zip.output_path
  function_name = "oap-frontend-amplify-apps-${var.environment}"
  role          = "arn:aws:iam::${var.accountId}:role/oap-lambda-exec-role-${var.environment}"
  handler       = "amplifyApps.handler"
  runtime       = "nodejs18.x"
  memory_size   = 128

  environment {
    variables = {
      ENVIRONMENT = var.environment
      HZU_AGENT_APP_ID = var.hzu_agent_app_id
      LSBFMYR_AGENT_APP_ID = var.lsbfmyr_agent_app_id
      UEG_AGENT_APP_ID = var.ueg_agent_app_id
      HZU_STUDENT_APP_ID  = var.hzu_student_app_id
      UNFC_STUDENT_APP_ID  = var.unfc_student_app_id
      UNFC_AGENT_APP_ID  = var.unfc_agent_app_id
      UCW_AGENT_APP_ID = var.ucw_agent_app_id
      UCW_STUDENT_APP_ID  = var.ucw_student_app_id
      LIM_AGENT_APP_ID  = var.lim_agent_app_id
      LIM_STUDENT_APP_ID = var.lim_student_app_id
      IBAT_AGENT_APP_ID  = var.ibat_agent_app_id
      UEG_STUDENT_APP_ID = var.ueg_student_app_id
      ARD_AGENT_APP_ID = var.ard_agent_app_id
      WUL_AGENT_APP_ID = var.wul_agent_app_id
      PU_AGENT_APP_ID = var.pu_agent_app_id
      OAP_FRONTEND_BUCKET = "oap-frontend-${var.environment}"
    }
  }

  tags = {
      Environment = var.environment_tag
      PROJECT     = "OAP"
      TEAM        = "EIP Development Team"
    }
}

resource "aws_lambda_permission" "allow_codecommit" {
  statement_id  = "AllowCodeCommitInvoke"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.amplify_app.function_name
  principal     = "codecommit.amazonaws.com"
  source_arn    = "arn:aws:codecommit:${var.region}:${var.accountId}:oap-frontend"
}
