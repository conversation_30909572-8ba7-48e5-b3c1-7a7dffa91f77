variable "region"{
    type = string
}

variable "environment"{
    type = string
}

variable "environment_tag"{
    type = string
}

variable "accountId"{
    type = string
}

variable "hzu_agent_app_id"{
    type = string
}

variable "lsbfmyr_agent_app_id"{
    type = string
}
variable "ueg_agent_app_id"{
  type = string
}
variable "hzu_student_app_id" {
    type = string
}
variable "unfc_student_app_id"{
    type = string
}
variable "unfc_agent_app_id" {
    type = string
}
variable "ucw_agent_app_id" {
  type = string
}
variable "ucw_student_app_id"{
  type = string
}
variable "lim_agent_app_id"{
  type = string
}
variable "lim_student_app_id" {
  type = string
}
variable "ibat_agent_app_id"{
  type = string
}
variable "ueg_student_app_id"{
    type = string
}
variable "ard_agent_app_id"{
  type = string
}
variable "wul_agent_app_id"{
  type = string
}

variable "pu_agent_app_id"{
  type = string
}
