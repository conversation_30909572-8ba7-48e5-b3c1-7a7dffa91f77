const AWS = require("aws-sdk");
const fs = require("fs");
const amplify = new AWS.Amplify();
const s3 = new AWS.S3();
const axios = require("axios");

module.exports.handler = async (event) => {
  try {
    console.log("Event:", JSON.stringify(event));

    const records = event.Records[0];
    const branchName = records.codecommit.references[0]?.ref?.replace(
      "refs/heads/",
      ""
    );
    if (!branchName) {
      throw new Error("Branch name could not be determined.");
    }

    const amplifyAppMapping = {
      [`lsbfmyr-${process.env.ENVIRONMENT}`]: process.env.LSBFMYR_AGENT_APP_ID,
      [`hzu-${process.env.ENVIRONMENT}`]: process.env.HZU_AGENT_APP_ID,
      [`hzu-student-${process.env.ENVIRONMENT}`]:
        process.env.HZU_STUDENT_APP_ID,
      [`unfc-student-${process.env.ENVIRONMENT}`]:
        process.env.UNFC_STUDENT_APP_ID,
      [`unfc-${process.env.ENVIRONMENT}`]: process.env.UNFC_AGENT_APP_ID,
      [`ucw-agent-${process.env.ENVIRONMENT}`]: process.env.UCW_AGENT_APP_ID,
      [`ucw-student-${process.env.ENVIRONMENT}`]:
        process.env.UCW_STUDENT_APP_ID,
      [`lim-${process.env.ENVIRONMENT}`]: process.env.LIM_AGENT_APP_ID,
      [`lim-student-${process.env.ENVIRONMENT}`]:
        process.env.LIM_STUDENT_APP_ID,
      [`ibat-${process.env.ENVIRONMENT}`]: process.env.IBAT_AGENT_APP_ID,
      [`ueg-${process.env.ENVIRONMENT}`]: process.env.UEG_AGENT_APP_ID,
      [`ueg-student-${process.env.ENVIRONMENT}`]:
        process.env.UEG_STUDENT_APP_ID,
      [`ard-${process.env.ENVIRONMENT}`]:
        process.env.ARD_AGENT_APP_ID,
      [`wul-${process.env.ENVIRONMENT}`]:
        process.env.WUL_AGENT_APP_ID,
      [`pu-${process.env.ENVIRONMENT}`]:
        process.env.PU_AGENT_APP_ID,
    };

    console.log("amplifyAppMapping", amplifyAppMapping);

    const appId = amplifyAppMapping[branchName];
    if (!appId) {
      console.log(`No Amplify app found for branch: ${branchName}`);
      return;
    }

    const appInfo = await amplify.getApp({ appId }).promise();
    console.log("appInfo ->", appInfo);
    const repoConnection = appInfo?.app?.repository;
    console.log("RepoConnection ->", repoConnection);
    if (repoConnection) {
      console.log(`App "${appId}" is connected to repository.`);
      await amplify
        .startJob({
          appId,
          branchName,
          jobType: "RELEASE",
        })
        .promise();
      console.log(`Triggered Amplify app: ${appId} for branch: ${branchName}`);
    } else {
      console.log(
        `App "${appId}" is not connected to a repository. Using manual deployment.`
      );
    }
  } catch (error) {
    console.error("Failed to trigger Amplify app:", error);
    throw error;
  }
};
