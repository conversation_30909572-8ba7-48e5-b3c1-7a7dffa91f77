resource "aws_s3_bucket" "enrollment_job_bucket" {
  bucket = "gus-oap-platform-event-listener-pipeline-${var.environment}"

  tags = {
    Name        = "gus-oap-platform-event-listener-pipeline-${var.environment}"
    Environment = "${var.environment}"
  }
}

resource "aws_codepipeline" "enrollment_pipeline" {
  name     = "gus-oap-platform-event-listener-pipeline-${var.environment}"
  role_arn = aws_iam_role.pipeline_role.arn

  artifact_store {
    location = "gus-oap-platform-event-listener-pipeline-${var.environment}"
    type     = "S3"
  }

  stage {
    name = "Source"

    action {
      name             = "SourceAction"
      category         = "Source"
      owner            = "AWS"
      provider         = "CodeCommit"
      version          = "1"
      output_artifacts = ["source_output"]

      configuration = {
        RepositoryName = "gus-oap-platform-event-listener"
        BranchName     = "${var.environment}"
        PollForSourceChanges = "false"
      }
    }
  }

  stage {
    name = "Build"

    action {
      name             = "BuildAction"
      category         = "Build"
      owner            = "AWS"
      provider         = "CodeBuild"
      version          = "1"
      input_artifacts  = ["source_output"]
      output_artifacts = ["build_output"]

      configuration = {
        ProjectName = "gus-oap-platform-event-listener-${var.environment}"
      }
    }
  }
  #   # DEPLOY
   stage {
     name = "Deploy"
     action {
       name            = "Deploy"
       category        = "Deploy"
       owner           = "AWS"
       provider        = "ECS"
       version         = "1"
       input_artifacts = ["build_output"]
       configuration = {
         ClusterName = "eip-integration-cluster-${var.environment}"
         ServiceName = "gus-oap-platform-event-listener-${var.environment}"
         FileName    = "imagedefinitions.json"
       }
     }
   }
 }


resource "aws_iam_role" "pipeline_role" {
  name = "gus-oap-platform-event-listener-pipeline-role-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codepipeline.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "code_pipeline" {
  name        = "gus-oap-platform-event-listener-policy-${var.environment}"
  description = "An kyb enrollment IAM policy"
  policy      = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
            "Action": [
                "codecommit:CancelUploadArchive",
                "codecommit:GetBranch",
                "codecommit:GetCommit",
                "codecommit:GetRepository",
                "codecommit:GetUploadArchiveStatus",
                "codecommit:UploadArchive",
                "codebuild:StartBuild",
                "codebuild:BatchGetBuilds",
                "s3:GetObject",
                "s3:PutObject",
                "s3:PutBucketPolicy",
                "s3:ListBucket",
                "s3:CreateBucket",
                "ecs:RegisterTaskDefinition",
                "ecs:UpdateService",
                "ecs:DescribeClusters",
                "ecs:DescribeTaskDefinition"
            ],
            "Resource": "*",
            "Effect": "Allow"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "pipeline_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = aws_iam_policy.code_pipeline.arn
}

resource "aws_iam_role_policy_attachment" "pipeline_policy_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AWSCodePipeline_FullAccess"
}

resource "aws_iam_role_policy_attachment" "ecs_policy_attachment" {
  role       = aws_iam_role.pipeline_role.name
  policy_arn = "arn:aws:iam::aws:policy/AmazonECS_FullAccess"
}

resource "aws_cloudwatch_event_rule" "gus_oap_platform_event_listener_codecommit_trigger_rule" {
  name = "gusOAPPlatformEventListenerCodecommitTriggerRule"
  event_pattern = jsonencode({
    source : ["aws.codecommit"],
    detail-type : ["CodeCommit Repository State Change"],
    resources : ["arn:aws:codecommit:${var.region}:${var.accountId}:gus-oap-platform-event-listener"],
    detail : {
      event : ["referenceCreated", "referenceUpdated"],
      referenceType : ["branch"],
      referenceName : ["${var.environment}"]
    }
  })
  tags = {
    Environment = var.environment_tag
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_event_target" "gus_oap_platform_event_listener_codecommit_event_target" {
  rule      = aws_cloudwatch_event_rule.gus_oap_platform_event_listener_codecommit_trigger_rule.name
  target_id = "gusOAPPlatformEventListenerCodecommitEventTarget"
  arn       = aws_codepipeline.enrollment_pipeline.arn
  role_arn  = "arn:aws:iam::${var.accountId}:role/events-codepipeline-eip-integration-access-role"
}