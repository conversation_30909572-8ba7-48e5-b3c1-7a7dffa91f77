resource "aws_iam_role" "codebuild_role" {
  name = "gus-oap-platform-event-listener-${var.environment}"

  assume_role_policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "codebuild.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF
}

resource "aws_iam_policy" "codebuild_policy" {
  name        = "gus-oap-platform-event-listener-codebuild-policy-${var.environment}"
  description = "IAM policy for CodeBuild project"

  policy = <<EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents",
        "ecr:GetAuthorizationToken",
        "ecr:BatchCheckLayerAvailability",
        "ecr:BatchGetImage",
        "ecr:PutImage",
        "ecr:InitiateLayerUpload",
        "ecr:UploadLayerPart",
        "ecr:CompleteLayerUpload",
        "s3:GetObject",
        "s3:ListBucket",
        "s3:CreateBucket",
        "s3:PutBucketPolicy",
        "s3:PutObject"
        
      ],
      "Resource": "*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "codebuild:CreateReportGroup",
        "codebuild:CreateReport",
        "codebuild:UpdateReport",
        "codebuild:BatchPutTestCases",
        "codebuild:BatchPutCodeCoverages",
        "codebuild:StartBuild",
        "codebuild:BatchGetBuilds"
      ],
      "Resource": "*"
    }
  ]
}
EOF
}

resource "aws_iam_role_policy_attachment" "codebuild_policy_attachment" {
  role       = aws_iam_role.codebuild_role.name
  policy_arn = aws_iam_policy.codebuild_policy.arn
}

resource "aws_codebuild_project" "code_build" {
  name         = "gus-oap-platform-event-listener-${var.environment}"
  description  = "gus-oap-platform-event-listener CodeBuild project created with Terraform"
  service_role = aws_iam_role.codebuild_role.arn
  artifacts {
    type = "CODEPIPELINE"
  }
  # Source configuration
  source {
    type = "CODEPIPELINE" # Use CodePipeline as source type
    # Define the buildspec content directly within Terraform
    buildspec = <<EOF
version: 0.2
phases:
  install:
    runtime-versions:
      nodejs: 18
    commands:
      # Install Node.js dependencies using npm or yarn
      - npm install -g pnpm
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - aws ecr get-login-password --region $AWS_DEFAULT_REGION | docker login --username AWS --password-stdin ${var.accountId}.dkr.ecr.$AWS_DEFAULT_REGION.amazonaws.com
      - REPOSITORY_URI=${var.accountId}.dkr.ecr.${var.region}.amazonaws.com/gus-oap-platform-event-listener-${var.environment}
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=build-$(echo $CODEBUILD_BUILD_ID | awk -F":" '{print $2}')
  build:
    commands:
      #- echo Build started on `date`
      - echo Building the Docker image...
      - docker build --build-arg NODE_ENV=${var.environment} -t $REPOSITORY_URI:latest .
      - docker tag $REPOSITORY_URI:latest $REPOSITORY_URI:$IMAGE_TAG
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push $REPOSITORY_URI:latest
      - docker push $REPOSITORY_URI:$IMAGE_TAG
      - echo Writing image definitions file...
      - printf '[{"name":"gus-oap-platform-event-listener-${var.environment}","imageUri":"%s"}]' $REPOSITORY_URI:$IMAGE_TAG > imagedefinitions.json
      - cat imagedefinitions.json 
artifacts:
    files: imagedefinitions.json
EOF
  }

  # Environment configuration
  environment {
    compute_type                = "BUILD_GENERAL1_SMALL"                           # Set compute type
    image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0" # Set Docker image for environment
    type                        = "LINUX_CONTAINER"                                # Set environment type
    image_pull_credentials_type = "CODEBUILD"                                      # Specify how CodeBuild obtains image pull credentials
  }
}
