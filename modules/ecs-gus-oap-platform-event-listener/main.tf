provider "aws" {
  region = var.region
}

# Create ecr repository
resource "aws_ecr_repository" "ecr_repo" {
  name = "gus-oap-platform-event-listener-${var.environment}"
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_security_group" "ecs_sg" {
  name        = "gus-oap-platform-event-listener-${var.environment}-sg"
  description = "Application sg for gus-oap-platform-event-listener"
  vpc_id      = var.aws_vpc[var.environment]
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "EIP"
    TEAM        = "EIP Development Team"
  }

  ingress {
    from_port   = 3008
    to_port     = 3008
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }

  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

resource "aws_ecr_lifecycle_policy" "lifecycle_policy" {
  repository = aws_ecr_repository.ecr_repo.name

  policy = jsonencode({
    rules = [
      {
        rulePriority = 1,
        description  = "in prod 10 images and in dev 3 images ",
        selection = {
          tagStatus   = "any",
          countType   = "imageCountMoreThan",
          countNumber = var.environment == "prod" ? 10 : 3 # Adjust retention period as needed
        },
        action = {
          type = "expire"
        }
      }
    ]
  })
}

# IAM Role for ECS Task
resource "aws_iam_role" "ecs_role" {
  name = "gus-oap-platform-event-listener-role-${var.environment}"

  assume_role_policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect = "Allow"
        Principal = {
          Service = ["ecs-tasks.amazonaws.com", "scheduler.amazonaws.com"]
        }
        Action = "sts:AssumeRole"
      }
    ]
  })
}

# IAM Policy for ECS Task
resource "aws_iam_policy" "ecs_policy" {
  name = "gus-oap-platform-event-listener-${var.environment}"
  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        # allow scheduler to execute the task
        Effect = "Allow",
        Action = [
          "ecs:RunTask"
        ]
        # trim :<revision> from arn, to point at the whole task definition and not just one revision
        Resource = [trimsuffix(aws_ecs_task_definition.task.arn, ":${aws_ecs_task_definition.task.revision}")]
      },
      { # allow kyb enrollment job to set the IAM roles of your task
        Effect = "Allow",
        Action = [
          "iam:PassRole",
          "events:*",
          "ecr:*",
          "logs:*",
          "ecs:*",
          "ec2:*",
          "dynamodb:*",
          "sqs:*",
          "sns:*"
        ]
        Resource = ["*"]
      },
    ]
  })
}

resource "aws_iam_role_policy_attachment" "policy_attachment" {
  role       = aws_iam_role.ecs_role.name
  policy_arn = aws_iam_policy.ecs_policy.arn
}

# Use local variables to reference the repository URI
locals {
  ecr_repo_uri   = aws_ecr_repository.ecr_repo.repository_url
  image_with_tag = "${local.ecr_repo_uri}:latest"
}

resource "aws_ecs_task_definition" "task" {
  requires_compatibilities = ["FARGATE"]
  cpu                      = 256
  memory                   = 512
  skip_destroy             = true
  network_mode             = "awsvpc"
  # role that allows ECS to spin up your task, for example needs permission to ECR to get container image
  execution_role_arn = aws_iam_role.ecs_role.arn
  # role that your workload gets to access AWS APIs
  task_role_arn = aws_iam_role.ecs_role.arn

  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64"
  }

  family = "gus-oap-platform-event-listener-${var.environment}"
  container_definitions = jsonencode([
    {
      name                     = "gus-oap-platform-event-listener-${var.environment}"
      image                    = local.image_with_tag
      cpu                      = 256
      memory                   = 512
      essential                = true
      requires_compatibilities = ["FARGATE"]
      network_mode             = "awsvpc"
      portMappings             = []
      logConfiguration = {
        logDriver = "awslogs"
        options = {
          awslogs-group         = aws_cloudwatch_log_group.log_group.name
          awslogs-region        = "${var.region}"
          awslogs-stream-prefix = "gus-oap-platform-event-listener"
        }
      }
    }
  ])
}

resource "aws_cloudwatch_log_group" "log_group" {
  name              = "gus-oap-platform-event-listener-${var.environment}" # Name of the log group
  retention_in_days = var.environment == "prod" ? 30 : 7      # Adjust retention period as needed  

}

resource "aws_ecs_service" "gus_salesforce_events_listener" {
  name            = "gus-oap-platform-event-listener-${var.environment}"
  cluster         = "eip-integration-cluster-${var.environment}"
  task_definition = aws_ecs_task_definition.task.arn
  desired_count   = 1
  launch_type     = "FARGATE"
  network_configuration {
    subnets = var.private_subnet_id[var.environment]
    security_groups = [aws_security_group.ecs_sg.id]
    assign_public_ip = false
  }
  tags = {
    Environment = "${var.environment}"
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
