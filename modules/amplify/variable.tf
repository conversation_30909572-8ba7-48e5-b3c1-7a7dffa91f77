variable "region" {
  description = "Aws region"
  type        = string

}

variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}



variable "oap_gateway_custom_domain" {
  description = ""
  type        = string
}

variable "ibat_oap_custom_domian_name" {
  description = ""
  type        = string
}


variable "lim_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "hzu_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "unfc_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "hzu_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "ucw_agent_oap_custom_domian_name" {
  description = ""
  type        = string
}
variable "ucw_student_oap_custom_domian_name" {
  description = ""
  type        = string
}
variable "s3_distribution_viewer_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

# LIM
variable "lim_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "unfc_student_oap_custom_domian_name" {
  description = ""
  type        = string
}