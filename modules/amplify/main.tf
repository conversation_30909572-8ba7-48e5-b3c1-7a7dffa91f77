provider "aws" {
  region = var.region
}

locals {
  common_build_spec = <<-EOT
    version: 0.1
    frontend:
      phases:
        preBuild:
          commands:
            - nvm install 22.16.0
            - nvm use 22.16.0
            - npm install
        build:
          commands:
            - echo "NEXT_PUBLIC_OAP_NAME=$NEXT_PUBLIC_OAP_NAME" > .env.local
            - echo "NEXT_PUBLIC_OAP_MODE=$NEXT_PUBLIC_OAP_MODE" >> .env.local
            - echo "NEXT_PUBLIC_OAP_TITLE=$NEXT_PUBLIC_OAP_TITLE" >> .env.local
            - echo "NEXT_PUBLIC_OAP_BACKEND_URL=$NEXT_PUBLIC_OAP_BACKEND_URL" >> .env.local
            - npm run build
      artifacts:
        baseDirectory: .next
        files:
          - '**/*'
      cache:
        paths:
          - node_modules/**/*
  EOT
}

// ibat-oap
resource "aws_amplify_app" "ibat-oap" {
  name                 = "ibat-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = false
  enable_auto_branch_creation = false
  
  # Disable the default Amplify domain
  enable_basic_auth = true
  basic_auth_credentials = base64encode("admin:${random_password.ibat_password.result}")
  
  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "IBAT_EL"
    "NEXT_PUBLIC_OAP_TITLE" : "	IBAT"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

# Generate a random password for basic auth
resource "random_password" "ibat_password" {
  length           = 16
  special          = true
  override_special = "!#$%&*()-_=+[]{}<>:?"
}

resource "aws_amplify_branch" "ibat-oap" {
  app_id      = aws_amplify_app.ibat-oap.id
  branch_name = "ibat-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
  
  # Enable basic auth with a complex random password
  enable_basic_auth = true
  basic_auth_credentials = base64encode("admin:${random_password.ibat_password.result}")
}

# Comment out or remove the domain association to make the app inaccessible
# resource "aws_amplify_domain_association" "ibat-oap" {
#   app_id                = aws_amplify_app.ibat-oap.id
#   domain_name           = "apphero.io"
#   wait_for_verification = false
#   lifecycle {
#     prevent_destroy = true       
#     ignore_changes  = all
#   }
#
#   sub_domain {
#       branch_name = aws_amplify_branch.ibat-oap.branch_name
#       prefix      = var.ibat_oap_custom_domian_name
#   }
# }

resource "aws_amplify_webhook" "ibat-oap" {
  app_id      = aws_amplify_app.ibat-oap.id
  branch_name = aws_amplify_branch.ibat-oap.branch_name
  description = "triggermaster"
}

// lim-oap
resource "aws_amplify_app" "lim-oap" {
  name                 = "lim-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "LIM"
    "NEXT_PUBLIC_OAP_TITLE" : "LIM"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "lim-oap" {
  app_id      = aws_amplify_app.lim-oap.id
  branch_name = "lim-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_domain_association" "lim-oap" {
  app_id                = aws_amplify_app.lim-oap.id
  domain_name           = "apphero.io"
  wait_for_verification = false
  lifecycle {
    prevent_destroy = true       
    ignore_changes  = all
  }

  sub_domain {
    branch_name = aws_amplify_branch.lim-oap.branch_name
    prefix      = var.lim_oap_custom_domian_name
  }
}

resource "aws_amplify_webhook" "lim-oap" {
  app_id      = aws_amplify_app.lim-oap.id
  branch_name = aws_amplify_branch.lim-oap.branch_name
  description = "lim-triggermaster"
}

// hzu-oap
resource "aws_amplify_app" "hzu-oap" {
  name                 = "hzu-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "HZU"
    "NEXT_PUBLIC_OAP_TITLE" : "HZU"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "hzu-oap" {
  app_id      = aws_amplify_app.hzu-oap.id
  branch_name = "hzu-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_domain_association" "hzu-oap" {
  app_id                = aws_amplify_app.hzu-oap.id
  domain_name           = "apphero.io"
  wait_for_verification = false
  lifecycle {
    prevent_destroy = true       
    ignore_changes  = all
  }

  sub_domain {
    branch_name = aws_amplify_branch.hzu-oap.branch_name
    prefix      = var.hzu_oap_custom_domian_name
  }
}

resource "aws_amplify_webhook" "hzu-oap" {
  app_id      = aws_amplify_app.hzu-oap.id
  branch_name = aws_amplify_branch.hzu-oap.branch_name
  description = "hzu-triggermaster"
}

// unfc-oap
resource "aws_amplify_app" "unfc-oap" {
  name                 = "unfc-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "UNFC"
    "NEXT_PUBLIC_OAP_TITLE" : "	UNFC"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "unfc-oap" {
  app_id      = aws_amplify_app.unfc-oap.id
  branch_name = "unfc-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_domain_association" "unfc-oap" {
  app_id                = aws_amplify_app.unfc-oap.id
  domain_name           = "apphero.io"
  wait_for_verification = false
  lifecycle {
    prevent_destroy = true       
    ignore_changes  = all
  }

  sub_domain {
    branch_name = aws_amplify_branch.unfc-oap.branch_name
    prefix      = var.unfc_oap_custom_domian_name
  }
}

resource "aws_amplify_webhook" "unfc-oap" {
  app_id      = aws_amplify_app.unfc-oap.id
  branch_name = aws_amplify_branch.unfc-oap.branch_name
  description = "triggermaster"
}

# //lsbfmyr-oap
resource "aws_amplify_app" "lsbfmyr_oap" {
  name                 = "lsbfmyr-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "LSBFMYR"
    "NEXT_PUBLIC_OAP_TITLE" : " LSBFMYR"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "lsbfmyr_oap" {
  app_id      = aws_amplify_app.lsbfmyr_oap.id
  branch_name = "lsbfmyr-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "lsbfmyr_oap" {
  app_id      = aws_amplify_app.lsbfmyr_oap.id
  branch_name = aws_amplify_branch.lsbfmyr_oap.branch_name
  description = "triggermaster"
}

# //ueg-oap
resource "aws_amplify_app" "ueg_oap" {
  name                 = "ueg-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "UEG"
    "NEXT_PUBLIC_OAP_TITLE" : " UEG"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "ueg_oap" {
  app_id      = aws_amplify_app.ueg_oap.id
  branch_name = "ueg-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "ueg_oap" {
  app_id      = aws_amplify_app.ueg_oap.id
  branch_name = aws_amplify_branch.ueg_oap.branch_name
  description = "triggermaster"
}

//ard-oap
resource "aws_amplify_app" "ard_oap" {
  name                 = "ard-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "ARD"
    "NEXT_PUBLIC_OAP_TITLE" : "ARD"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "ard_oap" {
  app_id      = aws_amplify_app.ard_oap.id
  branch_name = "ard-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "ard_oap" {
  app_id      = aws_amplify_app.ard_oap.id
  branch_name = aws_amplify_branch.ard_oap.branch_name
  description = "triggermaster"
}

//WUL
resource "aws_amplify_app" "wul_oap" {
  name                 = "wul-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "WUL"
    "NEXT_PUBLIC_OAP_TITLE" : "WUL"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "wul_oap" {
  app_id      = aws_amplify_app.wul_oap.id
  branch_name = "wul-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "wul_oap" {
  app_id      = aws_amplify_app.wul_oap.id
  branch_name = aws_amplify_branch.wul_oap.branch_name
  description = "triggermaster"
}

//PU
resource "aws_amplify_app" "pu_oap" {
  name                 = "pu-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "PU"
    "NEXT_PUBLIC_OAP_TITLE" : "PU"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "pu_oap" {
  app_id      = aws_amplify_app.pu_oap.id
  branch_name = "pu-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "pu_oap" {
  app_id      = aws_amplify_app.pu_oap.id
  branch_name = aws_amplify_branch.pu_oap.branch_name
  description = "triggermaster"
}


// hzu-student-oap
resource "aws_amplify_app" "hzu-student-oap" {
  name                 = "hzu-student-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "STUDENT"
    "NEXT_PUBLIC_OAP_NAME" : "HZU"
    "NEXT_PUBLIC_OAP_TITLE" : "	HZU"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com",
    "NEXT_PUBLIC_NODE_ENV":"${var.environment}"
  }
}

resource "aws_amplify_branch" "hzu-student-oap" {
  app_id      = aws_amplify_app.hzu-student-oap.id
  branch_name = "hzu-student-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

# resource "aws_amplify_domain_association" "hzu-student-oap" {
#   app_id                = aws_amplify_app.hzu-student-oap.id
#   domain_name           = "apphero.io"
#   wait_for_verification = false

#   sub_domain {
#     branch_name = aws_amplify_branch.hzu-student-oap.branch_name
#     prefix      = var.hzu_student_oap_custom_domian_name
#   }
# }

resource "aws_amplify_webhook" "hzu-studnet-oap" {
  app_id      = aws_amplify_app.hzu-student-oap.id
  branch_name = aws_amplify_branch.hzu-student-oap.branch_name
  description = "triggermaster"
}

// ucw-agent-oap
resource "aws_amplify_app" "ucw-agent-oap" {
  name                 = "ucw-agent-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "AGENT"
    "NEXT_PUBLIC_OAP_NAME" : "UCW"
    "NEXT_PUBLIC_OAP_TITLE" : "UCW"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com"
  }
}

resource "aws_amplify_branch" "ucw-agent-oap" {
  app_id      = aws_amplify_app.ucw-agent-oap.id
  branch_name = "ucw-agent-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}
resource "aws_amplify_domain_association" "ucw-agent-oap" {
  app_id                = aws_amplify_app.ucw-agent-oap.id
  domain_name           = "apphero.io"
  wait_for_verification = false
  lifecycle {
    prevent_destroy = true       
    ignore_changes  = all
  }
  # certificate_settings {
  #   type = "CUSTOM"
  #   custom_certificate_arn = var.s3_distribution_viewer_certificate_acm_certificate_arn
  # }
  sub_domain {
    branch_name = aws_amplify_branch.ucw-agent-oap.branch_name
    prefix      = var.ucw_agent_oap_custom_domian_name
  }
}

resource "aws_amplify_webhook" "ucw-agent-oap" {
  app_id      = aws_amplify_app.ucw-agent-oap.id
  branch_name = aws_amplify_branch.ucw-agent-oap.branch_name
  description = "ucw-agent-triggermaster"
}


// ucw-student-oap
resource "aws_amplify_app" "ucw-student-oap" {
  name                 = "ucw-student-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "STUDENT"
    "NEXT_PUBLIC_OAP_NAME" : "UCW"
    "NEXT_PUBLIC_OAP_TITLE" : "UCW"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com",
    "NEXT_PUBLIC_NODE_ENV":"${var.environment}"
  }
}

resource "aws_amplify_branch" "ucw-student-oap" {
  app_id      = aws_amplify_app.ucw-student-oap.id
  branch_name = "ucw-student-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_domain_association" "ucw-student-oap" {
  app_id                = aws_amplify_app.ucw-student-oap.id
  domain_name           = "ucanwest.ca"
  wait_for_verification = false

  sub_domain {
    branch_name = aws_amplify_branch.ucw-student-oap.branch_name
    prefix      = var.ucw_student_oap_custom_domian_name
  }
}

resource "aws_amplify_webhook" "ucw-student-oap" {
  app_id      = aws_amplify_app.ucw-student-oap.id
  branch_name = aws_amplify_branch.ucw-student-oap.branch_name
  description = "ucw-student-triggermaster"
}

// lim-student-oap
resource "aws_amplify_app" "lim-student-oap" {
  name                 = "lim-student-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "STUDENT"
    "NEXT_PUBLIC_OAP_NAME" : "LIM"
    "NEXT_PUBLIC_OAP_TITLE" : "	LIM"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023"
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com",
    "NEXT_PUBLIC_NODE_ENV":"${var.environment}"
  }
}

resource "aws_amplify_branch" "lim-student-oap" {
  app_id      = aws_amplify_app.lim-student-oap.id
  branch_name = "lim-student-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

# resource "aws_amplify_domain_association" "lim-student-oap" {
#   app_id                = aws_amplify_app.lim-student-oap.id
#   domain_name           = "apphero.io"
#   wait_for_verification = false

#   sub_domain {
#     branch_name = aws_amplify_branch.lim-student-oap.branch_name
#     prefix      = var.lim_student_oap_custom_domian_name
#   }
# }

resource "aws_amplify_webhook" "lim-studnet-oap" {
  app_id      = aws_amplify_app.lim-student-oap.id
  branch_name = aws_amplify_branch.lim-student-oap.branch_name
  description = "triggermaster"
}

// unfc-student-oap
resource "aws_amplify_app" "unfc-student-oap" {
  name                 = "unfc-student-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "STUDENT"
    "NEXT_PUBLIC_OAP_NAME" : "UNFC"
    "NEXT_PUBLIC_OAP_TITLE" : "UNFC"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com",
    "NEXT_PUBLIC_NODE_ENV":"${var.environment}"
  }
}

resource "aws_amplify_branch" "unfc-student-oap" {
  app_id      = aws_amplify_app.unfc-student-oap.id
  branch_name = "unfc-student-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

# resource "aws_amplify_domain_association" "unfc-student-oap" {
#   app_id                = aws_amplify_app.unfc-student-oap.id
#   domain_name           = "apphero.io"
#   wait_for_verification = false

#   sub_domain {
#     branch_name = aws_amplify_branch.unfc-student-oap.branch_name
#     prefix      = var.unfc_student_oap_custom_domian_name
#   }
# }

resource "aws_amplify_webhook" "unfc-student-oap" {
  app_id      = aws_amplify_app.unfc-student-oap.id
  branch_name = aws_amplify_branch.unfc-student-oap.branch_name
  description = "unfc-student-triggermaster"
}


// ueg-student-oap
resource "aws_amplify_app" "ueg-student-oap" {
  name                 = "ueg-student-oap"
  repository           = "https://git-codecommit.eu-west-1.amazonaws.com/v1/repos/oap-frontend"
  iam_service_role_arn = "arn:aws:iam::${var.accountId}:role/oap-amplify-start-access-${var.environment}"

  build_spec = local.common_build_spec

  platform = "WEB_COMPUTE"

  enable_branch_auto_build = true

  environment_variables = {
    "NEXT_PUBLIC_OAP_MODE" : "STUDENT"
    "NEXT_PUBLIC_OAP_NAME" : "UEG"
    "NEXT_PUBLIC_OAP_TITLE" : "UEG"
    "NEXT_PUBLIC_OAP_BACKEND_URL" : "https://${var.oap_gateway_custom_domain}"
    "_CUSTOM_IMAGE" : "amplify:al2023",
    "NEXT_PUBLIC_S3_DOMAIN" : "oap-backend-${var.environment}.s3.eu-west-1.amazonaws.com",
    "NEXT_PUBLIC_NODE_ENV":"${var.environment}"
  }
}

resource "aws_amplify_branch" "ueg-student-oap" {
  app_id      = aws_amplify_app.ueg-student-oap.id
  branch_name = "ueg-student-${var.environment}"
  framework   = "Next.js - SSR"
  stage       = "PRODUCTION"
}

resource "aws_amplify_webhook" "ueg-student-oap" {
  app_id      = aws_amplify_app.ueg-student-oap.id
  branch_name = aws_amplify_branch.ueg-student-oap.branch_name
  description = "ueg-student-triggermaster"
}
