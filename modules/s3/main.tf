provider "aws" {
  region = var.region
}
//lim
resource "aws_s3_bucket" "lim-application-exports" {
  bucket = "lim-application-exports-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
resource "aws_s3_bucket_public_access_block" "lim-application-exports_public_access_block" {
  bucket = aws_s3_bucket.lim-application-exports.id

  block_public_acls       = true
  block_public_policy     = true
  ignore_public_acls      = true
  restrict_public_buckets = true
}
//IBAT students application
resource "aws_s3_bucket" "ibat-student-application-storage" {
  bucket = "ibat-student-application-storage-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_notification" "bucket_notification" {
  bucket      = aws_s3_bucket.ibat-student-application-storage.id
  eventbridge = true
}


#resource "aws_s3_bucket_policy" "lim-application-exports_access_policy" {
#  bucket = aws_s3_bucket.lim-application-exports.id
#  policy = jsonencode({
#    Version = "2012-10-17"
#    Statement : [
#      {
#        Effect : "Allow",
#        Principal : "*",
#        Resource : [
#          aws_s3_bucket.lim-application-exports.arn,
#          "${aws_s3_bucket.lim-application-exports.arn}/*",
#        ]
#        Action : [
#          "s3:GetObject",
#          "s3:ListBucket",
#        ]
#      },
#    ]
#  })
#  depends_on = [aws_s3_bucket.lim-application-exports]
#  }
//oap-backend
resource "aws_s3_bucket" "oap-backend-service" {
  bucket = "oap-backend-service-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

//oap-handlers
resource "aws_s3_bucket" "oap-handlers" {
  bucket = "oap-handler-service-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}
//oap-pdf-generator
resource "aws_s3_bucket" "oap-pdf-generator" {
  bucket = "oap-pdf-generator-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

//oap-frontend
resource "aws_s3_bucket" "oap_frontend" {
  bucket = "oap-frontend-${var.environment}"
  
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_website_configuration" "oap_frontend" {
  bucket = aws_s3_bucket.oap_frontend.bucket

  index_document {
    suffix = "index.html"
  }

  error_document {
    key = "index.html"
  }
}


#resource "aws_s3_bucket_website" "oap_frontend_bucket_website" {
#  bucket = aws_s3_bucket.oap_frontend.id
#
#  index_document = "index.html"
#  error_document = "index.html"
#}

output "oap_frontend_website_endpoint" {
  value = aws_s3_bucket.oap_frontend.website_endpoint
}

resource "aws_s3_bucket_ownership_controls" "oap_frontend_ownership_controls" {
  bucket = aws_s3_bucket.oap_frontend.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "oap_frontend_public_access_block" {
  bucket = aws_s3_bucket.oap_frontend.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "oap_frontend_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.oap_frontend_ownership_controls,
    aws_s3_bucket_public_access_block.oap_frontend_public_access_block,
  ]

  bucket = aws_s3_bucket.oap_frontend.id
  acl    = "public-read"
}

resource "aws_s3_bucket" "oap_logs_exports" {
  bucket = "oap-logs-exports-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_ownership_controls" "oap_logs_exports_ownership_controls" {
  bucket = aws_s3_bucket.oap_logs_exports.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "oap_logs_exports_public_access_block" {
  bucket = aws_s3_bucket.oap_logs_exports.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "oap_logs_exports_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.oap_logs_exports_ownership_controls,
    aws_s3_bucket_public_access_block.oap_logs_exports_public_access_block,
  ]

  bucket = aws_s3_bucket.oap_logs_exports.id
  acl    = "public-read"
}

resource "aws_s3_bucket" "lim_csv_log_exports" {
  bucket = "lim-csv-log-exports-${var.environment}"
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_ownership_controls" "lim_csv_log_exports_ownership_controls" {
  bucket = aws_s3_bucket.lim_csv_log_exports.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_public_access_block" "lim_csv_log_exports_public_access_block" {
  bucket = aws_s3_bucket.lim_csv_log_exports.id

  block_public_acls       = false
  block_public_policy     = false
  ignore_public_acls      = false
  restrict_public_buckets = false
}

resource "aws_s3_bucket_acl" "lim_csv_log_exports_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.lim_csv_log_exports_ownership_controls,
    aws_s3_bucket_public_access_block.lim_csv_log_exports_public_access_block,
  ]

  bucket = aws_s3_bucket.lim_csv_log_exports.id
  acl    = "public-read"
}



resource "aws_s3_bucket_policy" "oap_frontend_access_policy" {
  bucket = aws_s3_bucket.oap_frontend.id
  policy = jsonencode({
    Version = "2012-10-17"
    Statement : [
      {
        Effect : "Allow",
        Principal : "*",
        Resource : [
          aws_s3_bucket.oap_frontend.arn,
          "${aws_s3_bucket.oap_frontend.arn}/*",
        ]
        Action : [
          "s3:GetObject",
          "s3:ListBucket",
        ]
      },
    ]
  })
}

resource "aws_s3_bucket_cors_configuration" "oap_frontend_cors_policy" {
  bucket = aws_s3_bucket.oap_frontend.id

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["PUT", "POST", "DELETE"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = [""]
    max_age_seconds = 3000
  }

  cors_rule {
    allowed_headers = ["*"]
    allowed_methods = ["GET", "PUT", "POST", "DELETE", "HEAD"]
    allowed_origins = ["http://localhost:3000"]
    expose_headers  = ["Access-Control-Allow-Origin"]
  }

  cors_rule {
    allowed_headers = [""]
    allowed_methods = ["GET"]
    allowed_origins = ["*"]
    expose_headers  = [""]
  }
}

//ucw_migration_applications
resource "aws_s3_bucket" "ucw_migration_applications" {
  bucket = "ucw-migration-applications-${var.environment}"
    tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_s3_bucket_ownership_controls" "ucw_migration_applications_ownership" {
  bucket = aws_s3_bucket.ucw_migration_applications.id
  rule {
    object_ownership = "BucketOwnerPreferred"
  }
}

resource "aws_s3_bucket_acl" "ucw_migration_applications_bucket_acl" {
  depends_on = [
    aws_s3_bucket_ownership_controls.ucw_migration_applications_ownership
  ]

  bucket = aws_s3_bucket.ucw_migration_applications.id
  acl    = "private"
}

resource "aws_s3_bucket_policy" "ucw_migration_applications_policy" {
  bucket = aws_s3_bucket.ucw_migration_applications.bucket

  policy = jsonencode({
    Version = "2012-10-17"
    Statement = [
      {
        Effect    = "Allow"
        Principal = {
          "AWS" = "arn:aws:iam::${var.accountId}:user/ucw-legacy-application-user-${var.environment}"
        }
        Action   = [
          "s3:ListBucket",                
          "s3:PutObject",                 
          "s3:AbortMultipartUpload",      
          "s3:ListMultipartUploadParts",  
          "s3:ListBucketMultipartUploads", 
          "s3:GetBucketAcl",            
          "s3:PutObjectAcl"               
        ]
        Resource = [
          "arn:aws:s3:::${aws_s3_bucket.ucw_migration_applications.bucket}", 
          "arn:aws:s3:::${aws_s3_bucket.ucw_migration_applications.bucket}/*"
        ]
      } 
    ]
  })
}
