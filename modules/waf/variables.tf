variable "environment" {
  description = "Deployment environment (dev/stage/prod)"
  type        = string
}

variable "region" {
  description = "AWS region"
  type        = string
  default     = "eu-west-1"
}

variable "accountId" {
  description = "AWS account ID"
  type        = string
}

variable "api_gateway_arn" {
  description = "Stage ARN of the API Gateway to associate WAF with (format: arn:aws:apigateway:region::/restapis/api-id/stages/stage-name)"
  type        = string
}

variable "amplify_waf_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for CloudFront WAF logs"
  type        = string
}

variable "backend_waf_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for Backend WAF logs"
  type        = string
}
