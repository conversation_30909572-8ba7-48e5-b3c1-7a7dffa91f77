provider "aws" {
  region = var.region
}

# CloudFront WAF resources must be created in us-east-1
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}

# Get current AWS account ID
data "aws_caller_identity" "current" {}

locals {
  waf_tags = {
    Environment = var.environment
    Project     = "OAP"
    ManagedBy   = "Terraform"
  }
}

# CloudFront WAF must be created in us-east-1 with CLOUDFRONT scope
resource "aws_wafv2_web_acl" "oap_amplify_acl" {
  provider    = aws.us_east_1
  name        = "oap-amplify-waf-${var.environment}"
  description = "WAF for CloudFront distribution - Amplify frontend"
  scope       = "CLOUDFRONT"
  tags        = local.waf_tags

  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "OAPAmplifyWAF"
    sampled_requests_enabled   = true
  }

  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CommonRuleSetAmplify"
      sampled_requests_enabled   = true
    }
  }

  # Additional rules commented out to avoid extra billing
  # rule {
  #   name     = "AWSManagedRulesKnownBadInputsRuleSet"
  #   priority = 2
  #   override_action {
  #     none {}
  #   }
  #   statement {
  #     managed_rule_group_statement {
  #       name        = "AWSManagedRulesKnownBadInputsRuleSet"
  #       vendor_name = "AWS"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "KnownBadInputsAmplify"
  #     sampled_requests_enabled   = true
  #   }
  # }

  # rule {
  #   name     = "AWSManagedRulesAmazonIpReputationList"
  #   priority = 3
  #   override_action {
  #     none {}
  #   }
  #   statement {
  #     managed_rule_group_statement {
  #       name        = "AWSManagedRulesAmazonIpReputationList"
  #       vendor_name = "AWS"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "AmazonIpReputationAmplify"
  #     sampled_requests_enabled   = true
  #   }
  # }

  # rule {
  #   name     = "RateLimitRule"
  #   priority = 4
  #   action {
  #     block {}
  #   }
  #   statement {
  #     rate_based_statement {
  #       limit              = 2000
  #       aggregate_key_type = "IP"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "RateLimitAmplify"
  #     sampled_requests_enabled   = true
  #   }
  # }
}

resource "aws_wafv2_web_acl" "oap_backend_api_acl" {
  name        = "oap-backend-api-waf-${var.environment}"
  description = "WAF for API Gateway backend"
  scope       = "REGIONAL"
  tags        = local.waf_tags

  default_action {
    allow {}
  }

  visibility_config {
    cloudwatch_metrics_enabled = true
    metric_name                = "OAPBackendAPIWAF"
    sampled_requests_enabled   = true
  }

  # Rule to allow large payloads ONLY for PDF generation endpoint
  # This rule has higher priority and allows PDF requests before they hit size restrictions
  rule {
    name     = "AllowLargePayloadsForPDF"
    priority = 0

    action {
      allow {}
    }

    statement {
      byte_match_statement {
        search_string = "oap/generatepdf"  # Replace with your actual PDF endpoint
        field_to_match {
          uri_path {}
        }
        text_transformation {
          priority = 0
          type     = "LOWERCASE"
        }
        positional_constraint = "CONTAINS"
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "AllowLargePayloadsForPDF"
      sampled_requests_enabled   = true
    }
  }

  rule {
    name     = "AWSManagedRulesCommonRuleSet"
    priority = 1

    override_action {
      none {}
    }

    statement {
      managed_rule_group_statement {
        name        = "AWSManagedRulesCommonRuleSet"
        vendor_name = "AWS"

        # Exclude size restrictions only for PDF generation endpoints
        rule_action_override {
          action_to_use {
            allow {}
          }
          name = "SizeRestrictions_BODY"
        }
      }
    }

    visibility_config {
      cloudwatch_metrics_enabled = true
      metric_name                = "CommonRuleSetBackend"
      sampled_requests_enabled   = true
    }
  }

  # Additional rules commented out to avoid extra billing
  # rule {
  #   name     = "AWSManagedRulesKnownBadInputsRuleSet"
  #   priority = 2
  #   override_action {
  #     none {}
  #   }
  #   statement {
  #     managed_rule_group_statement {
  #       name        = "AWSManagedRulesKnownBadInputsRuleSet"
  #       vendor_name = "AWS"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "KnownBadInputsBackend"
  #     sampled_requests_enabled   = true
  #   }
  # }

  # rule {
  #   name     = "AWSManagedRulesAmazonIpReputationList"
  #   priority = 3
  #   override_action {
  #     none {}
  #   }
  #   statement {
  #     managed_rule_group_statement {
  #       name        = "AWSManagedRulesAmazonIpReputationList"
  #       vendor_name = "AWS"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "AmazonIpReputationBackend"
  #     sampled_requests_enabled   = true
  #   }
  # }

  # rule {
  #   name     = "AWSManagedRulesSQLiRuleSet"
  #   priority = 4
  #   override_action {
  #     none {}
  #   }
  #   statement {
  #     managed_rule_group_statement {
  #       name        = "AWSManagedRulesSQLiRuleSet"
  #       vendor_name = "AWS"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "SQLiRuleSetBackend"
  #     sampled_requests_enabled   = true
  #   }
  # }

  # rule {
  #   name     = "RateLimitRule"
  #   priority = 5
  #   action {
  #     block {}
  #   }
  #   statement {
  #     rate_based_statement {
  #       limit              = 1000
  #       aggregate_key_type = "IP"
  #     }
  #   }
  #   visibility_config {
  #     cloudwatch_metrics_enabled = true
  #     metric_name                = "RateLimitBackend"
  #     sampled_requests_enabled   = true
  #   }
  # }
}

# Optional association for API Gateway (Amplify doesn’t support it yet via TF)
resource "aws_wafv2_web_acl_association" "api_gateway_assoc" {
  resource_arn = var.api_gateway_arn
  web_acl_arn  = aws_wafv2_web_acl.oap_backend_api_acl.arn

  depends_on = [aws_wafv2_web_acl.oap_backend_api_acl]
}



# WAF Logging Configuration for CloudFront - enabled for monitoring
resource "aws_wafv2_web_acl_logging_configuration" "amplify_waf_logging" {
  provider                = aws.us_east_1
  resource_arn            = aws_wafv2_web_acl.oap_amplify_acl.arn
  log_destination_configs = ["${var.amplify_waf_log_group_arn}:*"]

  redacted_fields {
    single_header {
      name = "authorization"
    }
  }

  redacted_fields {
    single_header {
      name = "cookie"
    }
  }

  depends_on = [aws_wafv2_web_acl.oap_amplify_acl]
}

# WAF Logging Configuration for API Gateway - enabled for monitoring
resource "aws_wafv2_web_acl_logging_configuration" "backend_waf_logging" {
  resource_arn            = aws_wafv2_web_acl.oap_backend_api_acl.arn
  log_destination_configs = ["${var.backend_waf_log_group_arn}:*"]

  redacted_fields {
    single_header {
      name = "authorization"
    }
  }

  redacted_fields {
    single_header {
      name = "cookie"
    }
  }

  depends_on = [aws_wafv2_web_acl.oap_backend_api_acl]
}


# CloudWatch Alarms for WAF monitoring - commented out until required
# resource "aws_cloudwatch_metric_alarm" "waf_blocked_requests_amplify" {
#   alarm_name          = "waf-blocked-requests-amplify-${var.environment}"
#   comparison_operator = "GreaterThanThreshold"
#   evaluation_periods  = "2"
#   metric_name         = "BlockedRequests"
#   namespace           = "AWS/WAFV2"
#   period              = "300"
#   statistic           = "Sum"
#   threshold           = "100"
#   alarm_description   = "This metric monitors blocked requests on Amplify WAF"
#   alarm_actions       = [] # Add SNS topic ARN here if needed

#   dimensions = {
#     WebACL = aws_wafv2_web_acl.oap_amplify_acl.name
#     Rule   = "ALL"
#     Region = "us-east-1"
#   }

#   tags = local.waf_tags
# }

# resource "aws_cloudwatch_metric_alarm" "waf_blocked_requests_backend" {
#   alarm_name          = "waf-blocked-requests-backend-${var.environment}"
#   comparison_operator = "GreaterThanThreshold"
#   evaluation_periods  = "2"
#   metric_name         = "BlockedRequests"
#   namespace           = "AWS/WAFV2"
#   period              = "300"
#   statistic           = "Sum"
#   threshold           = "50"
#   alarm_description   = "This metric monitors blocked requests on Backend API WAF"
#   alarm_actions       = [] # Add SNS topic ARN here if needed

#   dimensions = {
#     WebACL = aws_wafv2_web_acl.oap_backend_api_acl.name
#     Rule   = "ALL"
#     Region = var.region
#   }

#   tags = local.waf_tags
# }

# Outputs for debugging and reference
output "amplify_waf_arn" {
  description = "ARN of the CloudFront WAF Web ACL"
  value       = aws_wafv2_web_acl.oap_amplify_acl.arn
}

output "backend_waf_arn" {
  description = "ARN of the Regional WAF Web ACL"
  value       = aws_wafv2_web_acl.oap_backend_api_acl.arn
}

output "amplify_waf_logging_config_arn" {
  description = "ARN of the CloudFront WAF logging configuration"
  value       = aws_wafv2_web_acl_logging_configuration.amplify_waf_logging.resource_arn
}

output "backend_waf_logging_config_arn" {
  description = "ARN of the Backend WAF logging configuration"
  value       = aws_wafv2_web_acl_logging_configuration.backend_waf_logging.resource_arn
}

output "account_id" {
  description = "AWS Account ID (for debugging)"
  value       = data.aws_caller_identity.current.account_id
}
