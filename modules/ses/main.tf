provider "aws" {
  region = var.region
}

resource "aws_sesv2_email_identity" "lsbfm_email_identity" {
  email_identity = var.lsbfm_email_identity
}

resource "aws_sesv2_email_identity" "ucw_email_identity" {
  email_identity = var.ucw_email_identity
}

resource "aws_sesv2_email_identity" "ueg_email_identity" {
  email_identity = var.ueg_email_identity
}

resource "aws_sesv2_email_identity" "unfc_email_identity" {
  email_identity = var.unfc_email_identity
}
