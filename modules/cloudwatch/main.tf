provider "aws" {
  region = var.region
}

# CloudFront WAF logs must be created in us-east-1
provider "aws" {
  alias  = "us_east_1"
  region = "us-east-1"
}
resource "aws_cloudwatch_log_group" "oap_logger_log_group" {
  name              = "oap-loggers-${var.environment}"
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

resource "aws_cloudwatch_log_group" "lim_oap_csv_exporter_log_group" {
  name              = "lim-oap-csv-exporter-kpis-${var.environment}"
  retention_in_days = var.environment == "prod" ? 30 : 7  # Adjust retention period as needed

  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# CloudWatch Log Group for CloudFront WAF logs - must be in us-east-1
resource "aws_cloudwatch_log_group" "amplify_waf_log_group" {
  provider          = aws.us_east_1
  name              = "aws-waf-logs-oap-amplify-waf-${var.environment}"
  retention_in_days = var.environment == "prod" ? 30 : 7
  
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
    Purpose     = "WAF Logging - CloudFront"
  }
}

# CloudWatch Log Group for Backend API WAF logs - in the same region as backend
resource "aws_cloudwatch_log_group" "backend_waf_log_group" {
  name              = "aws-waf-logs-oap-backend-waf-${var.environment}"
  retention_in_days = var.environment == "prod" ? 30 : 7
  
  tags = {
    Environment = var.environment_tag
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
    Purpose     = "WAF Logging - Backend API"
  }
}

resource "aws_cloudwatch_log_resource_policy" "waf_logging_policy" {
  policy_name = "AWSWAFLoggingPolicy"

  policy_document = jsonencode({
    Version = "2012-10-17",
    Statement = [
      {
        Sid = "AllowWAFLoggingCloudFront",
        Effect = "Allow",
        Principal = {
          Service = "waf.amazonaws.com"
        },
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "${aws_cloudwatch_log_group.amplify_waf_log_group.arn}:*"
      },
      {
        Sid = "AllowWAFLoggingBackend",
        Effect = "Allow",
        Principal = {
          Service = "waf.amazonaws.com"
        },
        Action = [
          "logs:CreateLogStream",
          "logs:PutLogEvents"
        ],
        Resource = "${aws_cloudwatch_log_group.backend_waf_log_group.arn}:*"
      }
    ]
  })
}


output "waf_amplify_log_group_arn" {
  value = aws_cloudwatch_log_group.amplify_waf_log_group.arn
}

output "waf_backend_log_group_arn" {
  value = aws_cloudwatch_log_group.backend_waf_log_group.arn
}
