# Output log group names and ARNs for other modules to reference
output "oap_logger_log_group_name" {
  description = "Name of the OAP logger CloudWatch log group"
  value       = aws_cloudwatch_log_group.oap_logger_log_group.name
}

output "oap_logger_log_group_arn" {
  description = "ARN of the OAP logger CloudWatch log group"
  value       = aws_cloudwatch_log_group.oap_logger_log_group.arn
}

output "lim_oap_csv_exporter_log_group_name" {
  description = "Name of the LIM OAP CSV exporter CloudWatch log group"
  value       = aws_cloudwatch_log_group.lim_oap_csv_exporter_log_group.name
}

output "lim_oap_csv_exporter_log_group_arn" {
  description = "ARN of the LIM OAP CSV exporter CloudWatch log group"
  value       = aws_cloudwatch_log_group.lim_oap_csv_exporter_log_group.arn
}

output "amplify_waf_log_group_name" {
  description = "Name of the CloudWatch Log Group for CloudFront WAF"
  value       = aws_cloudwatch_log_group.amplify_waf_log_group.name
}

output "amplify_waf_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for CloudFront WAF"
  value       = aws_cloudwatch_log_group.amplify_waf_log_group.arn
}

output "backend_waf_log_group_name" {
  description = "Name of the CloudWatch Log Group for Backend WAF"
  value       = aws_cloudwatch_log_group.backend_waf_log_group.name
}

output "backend_waf_log_group_arn" {
  description = "ARN of the CloudWatch Log Group for Backend WAF"
  value       = aws_cloudwatch_log_group.backend_waf_log_group.arn
}
