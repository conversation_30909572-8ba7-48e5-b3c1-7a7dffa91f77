provider "aws" {
  region = var.region
}

# Step 1: Define the IAM user
resource "aws_iam_user" "lim_exporter_user" {
  name = "lim-export-user-${var.environment}"

  tags = {
    Environment = var.environment
    PROJECT     = "OAP"
    TEAM        = "EIP Development Team"
  }
}

# Step 2: Create an IAM policy allowing access to the specific S3 bucket
data "aws_iam_policy_document" "example_policy_document" {
  statement {
    actions   = ["s3:GetObject", "s3:PutObject", "s3:ListBucket"]
    resources = ["arn:aws:s3:::lim-application-exports-dev/*", "arn:aws:s3:::lim-application-exports-dev"]
  }
}

resource "aws_iam_policy" "lim_exporter_policy" {
  name        = "lim-s3-policy-${var.environment}"
  description = "Allows access to the example S3 bucket"
  policy      = data.aws_iam_policy_document.example_policy_document.json
}

# Step 3: Attach the IAM policy to the IAM user
resource "aws_iam_user_policy_attachment" "example_user_policy_attachment" {
  user       = aws_iam_user.lim_exporter_user.name
  policy_arn = aws_iam_policy.lim_exporter_policy.arn
}

# Step 4: Create IAM access keys for the user
resource "aws_iam_access_key" "example_user_access_key" {
  user = aws_iam_user.lim_exporter_user.name
}