variable "region" {
  description = "Aws region"
  type        = string

}
variable "environment" {
  description = "Environment"
  type        = string
}

variable "accountId" {
  description = "Environment"
  type        = string
}

variable "environment_tag" {
  description = "Environment Tag"
  type        = string
}

variable "ibat_oap_custom_domian_name" {
  description = ""
  type        = string
}


variable "lim_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "hzu_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "unfc_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "unfc_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "hzu_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "ucw_agent_oap_custom_domian_name" {
  description = ""
  type        = string
}
variable "ucw_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

variable "oap_frontend_project_source_type" {
  description = ""
  type        = string
}


variable "oap_frontend_project_build_timeout" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_compute_type" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_image" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_type" {
  description = ""
  type        = string
}

variable "oap_frontend_project_environment_image_pull_credentials_type" {
  description = ""
  type        = string
}


variable "oap_frontend_project_artifact_type" {
  description = ""
  type        = string
}

variable "oap_frontend_pipeline_artifact_store_location" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_artifact_store_type" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_source_config_repository_name" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_source_config_branch_name" {
  description = ""
  type        = string
}
variable "oap_frontend_pipeline_project_name" {
  description = ""
  type        = string
}

// cloudfront


variable "s3_distribution_oap_frontend_domain_name" {
  description = ""
  type        = string
}

variable "s3_distribution_oap_frontend_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_http_port" {
  description = ""
  type        = number
}

variable "s3_distribution_https_port" {
  description = ""
  type        = number
}

variable "s3_distribution_origin_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_origin_ssl_protocols" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_is_ipv6_enabled" {
  description = ""
  type        = bool
}

variable "s3_distribution_default_cache_behavior_allowed_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_default_cache_behavior_cached_methods" {
  description = ""
  type        = list(string)
}

variable "s3_distribution_apphero_default_cache_behavior_target_origin_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_cache_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_default_cache_behavior_response_headers_policy_id" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_protocol_policy" {
  description = ""
  type        = string
}

variable "s3_distribution_geo_restriction_restriction_type" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "s3_distribution_viewer_certificate_cloudfront_default_certificate" {
  description = ""
  type        = bool
}
variable "lambda_role_arn" {
  description = ""
  type        = string
}

variable "canada_central_region" {
  description = ""
  type        = string
}

variable "ireland_region" {
  description = ""
  type        = string
}
variable "oap_gateway_custom_domain" {
  description = ""
  type        = string
}
variable "oap_gateway_certificate_acm_certificate_arn" {
  description = ""
  type        = string
}

variable "s3_role_arn" {
  description = "s3 role arn "
  type        = string
}

variable "s3_cross_account_role_arn" {
  description = ""
  type        = string
}

variable "prod_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}
variable "dev_oap_lambda_assumed_role_arn" {
  description = ""
  type        = string
}
variable "dev_oap_lambda_notification_arn" {
  description = ""
  type        = string
}
variable "dev_eip_integration_lambda_arn" {
  description = ""
  type        = string
}

variable "prod_eip_integration_lambda_arn" {
  description = ""
  type        = string
}
variable "prod_oap_lambda_notification_arn" {
  description = ""
  type        = string
}

variable "dev_user_access_arn" {
  description = ""
  type        = string
}

variable "prod_user_access_arn" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_id" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_client_secret" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_oidc_issuer" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_authorize_scopes" {
  description = ""
  type        = string
}

variable "cognito_linkedin_identity_provider_attributes_request_method" {
  description = ""
  type        = string
}

variable "hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_callback_urls" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_logout_urls" {
  description = ""
  type        = list(string)
}

variable "hzu_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = ""
  type        = list(string)
}

variable "ses_mailer_arn" {
  description = ""
  type        = string
}

variable "ucw_ses_mailer_arn"{
  description = ""
  type        = string
}

variable "ueg_ses_mailer_arn"{
  description = ""
  type        = string
}

variable "hzu" {
  description = ""
  type        = string
}

variable "user_type_student" {
  description = ""
  type        = string
}

# LIM

variable "lim_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = ""
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = ""
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = ""
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_callback_urls" {
  description = ""
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_logout_urls" {
  description = ""
  type        = list(string)
}

variable "lim_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = ""
  type        = list(string)
}

variable "lim" {
  description = ""
  type        = string
}

variable "lim_student_oap_custom_domian_name" {
  description = ""
  type        = string
}

# UCW - student
variable "ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UCW student Cognito user pool client"
  type        = list(string)
}

variable "ucw" {
  description = ""
  type        = string
}


# UNFC - student
variable "unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UNFC student Cognito user pool client"
  type        = list(string)
}

variable "unfc" {
  description = ""
  type        = string
}

//UE
variable "ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows" {
  description = "Allowed OAuth flows for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes" {
  description = "Allowed OAuth scopes for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_explicit_auth_flows" {
  description = "Explicit auth flows for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_callback_urls" {
  description = "Callback URLs for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_logout_urls" {
  description = "Logout URLs for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg_student_oap_cognito_user_pool_client_supported_identity_providers" {
  description = "Supported identity providers for UEG student Cognito user pool client"
  type        = list(string)
}

variable "ueg" {
  description = ""
  type        = string
}

#ses
variable "lsbfm_email_identity"{
  type        = string
}

variable "ucw_email_identity"{
  description = ""
  type        = string
}

variable "ueg_email_identity" {
  type        = string
}

variable "unfc_email_identity" {
  type        = string
}

#Amplify AppId's
variable "hzu_agent_app_id"{
    type = string
}

variable "lsbfmyr_agent_app_id"{
    type = string
}

variable "ueg_agent_app_id"{
    type = string
}

variable "ueg_student_app_id"{
    type = string
}

variable "hzu_student_app_id" {
    type = string
}
variable "unfc_student_app_id"{
    type = string
}
variable "unfc_agent_app_id" {
    type = string
}
variable "ucw_agent_app_id" {
  type = string
}
variable "ucw_student_app_id"{
  type = string
}
variable "lim_agent_app_id"{
  type = string
}
variable "lim_student_app_id" {
  type = string
}
variable "ibat_agent_app_id"{
  type = string
}
variable "ard_agent_app_id"{
  type = string
}
variable "wul_agent_app_id"{
  type = string
}
variable "pu_agent_app_id"{
  type = string
}

variable "api_gateway_arn" {
  description = "Stage ARN of the API Gateway to associate WAF with (format: arn:aws:apigateway:region::/restapis/api-id/stages/stage-name)"
  type        = string
}