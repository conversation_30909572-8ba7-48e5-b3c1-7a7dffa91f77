region          = "eu-west-1"
environment     = "dev"
accountId       = "************"
environment_tag = "DEV"

//gateway
oap_gateway_certificate_acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/35116c27-4b9b-4a39-8cf5-34d7cd6b2f36"
oap_gateway_custom_domain                   = "oap-dev-api.apphero.io"

//IAM
lambda_role_arn                  = "arn:aws:iam::************:role/gus-lambda-exec-role-dev"
s3_cross_account_role_arn        = "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod"
prod_oap_lambda_assumed_role_arn = "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-prod/oap-backend-service-prod"
dev_oap_lambda_assumed_role_arn  = "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-dev/oap-backend-service-dev"
dev_oap_lambda_notification_arn  = "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-dev/oap-notifications-dev"
dev_eip_integration_lambda_arn   =  "arn:aws:sts::************:assumed-role/gus-eip-integration-handlers-access-dev/gus-sf-my-ucw-integration-dev"
prod_eip_integration_lambda_arn  =  "arn:aws:sts::************:assumed-role/gus-eip-integration-handlers-access-prod/gus-sf-my-ucw-integration-prod"
prod_oap_lambda_notification_arn = "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-prod/oap-notifications-prod"
prod_user_access_arn             = "arn:aws:iam::************:user/<EMAIL>"
dev_user_access_arn              = "arn:aws:iam::************:user/nishanth.g"
// code build
oap_frontend_project_source_type                             = "CODEPIPELINE"
oap_frontend_project_build_timeout                           = "5"
oap_frontend_project_environment_compute_type                = "BUILD_GENERAL1_SMALL"
oap_frontend_project_environment_image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
oap_frontend_project_environment_type                        = "LINUX_CONTAINER"
oap_frontend_project_environment_image_pull_credentials_type = "CODEBUILD"
oap_frontend_project_artifact_type                           = "CODEPIPELINE"

// codepipeline
oap_frontend_pipeline_artifact_store_location       = "oap-frontend"
oap_frontend_pipeline_artifact_store_type           = "S3"
oap_frontend_pipeline_source_config_repository_name = "oap-frontend"
oap_frontend_pipeline_source_config_branch_name     = "dev"
oap_frontend_pipeline_project_name                  = "oap-frontend"

// cloudfront
s3_distribution_oap_frontend_domain_name                          = "oap-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_oap_frontend_origin_id                            = "oap-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_http_port                                         = 80
s3_distribution_https_port                                        = 443
s3_distribution_origin_protocol_policy                            = "http-only"
s3_distribution_origin_ssl_protocols                              = ["TLSv1.2"]
s3_distribution_enabled                                           = true
s3_distribution_is_ipv6_enabled                                   = true
s3_distribution_viewer_certificate_cloudfront_default_certificate = true
s3_distribution_default_cache_behavior_allowed_methods            = ["GET", "HEAD", "OPTIONS"]
s3_distribution_default_cache_behavior_cached_methods             = ["GET", "HEAD"]
s3_distribution_apphero_default_cache_behavior_target_origin_id   = "oap-frontend-dev.s3-website-eu-west-1.amazonaws.com"
s3_distribution_default_cache_behavior_cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
s3_distribution_default_cache_behavior_response_headers_policy_id = "eaab4381-ed33-4a86-88ca-d9558dc6cd63"
s3_distribution_viewer_protocol_policy                            = "redirect-to-https"
s3_distribution_geo_restriction_restriction_type                  = "none"
s3_distribution_viewer_certificate_acm_certificate_arn            = "arn:aws:acm:us-east-1:************:certificate/35116c27-4b9b-4a39-8cf5-34d7cd6b2f36"

//sqs
canada_central_region = "ca-central-1"
ireland_region        = "eu-west-1"

//amplify 
lim_oap_custom_domian_name         = "lim-oap-dev"
ibat_oap_custom_domian_name        = "ibatel-oap-dev"
hzu_oap_custom_domian_name         = "hzu-oap-dev"
unfc_oap_custom_domian_name         = "unfc-oap-dev"
ucw_student_oap_custom_domian_name = "apply-dev"
hzu_student_oap_custom_domian_name = "hzu-student-oap-dev"
ucw_agent_oap_custom_domian_name   = "ucw-oap-dev"
lim_student_oap_custom_domian_name = "lim-student-oap-dev"
unfc_student_oap_custom_domian_name = "unfc-student-oap-dev"

//ecs lim oap
s3_role_arn = "arn:aws:iam::************:role/s3CrossAccountAccessRole-prod"

//vpc
//availability_zones = ["eu-west-1a", "eu-west-1b"]

//cognito
cognito_linkedin_identity_provider_client_id                          = "867qbkvwgfs1cb"
cognito_linkedin_identity_provider_client_secret                      = "Lqvn7ypbfXHopDkC"
cognito_linkedin_identity_provider_oidc_issuer                        = "https://www.linkedin.com/oauth"
cognito_linkedin_identity_provider_authorize_scopes                   = "email openid profile"
cognito_linkedin_identity_provider_attributes_request_method          = "GET"
hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
hzu_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
hzu_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
hzu_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
hzu_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ses_mailer_arn                                                        = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>"
ucw_ses_mailer_arn                                                    = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>"
ueg_ses_mailer_arn                                                     = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>" //<EMAIL> 
hzu                                                                   = "hzu"
user_type_student                                                     = "student"
lim_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
lim_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
lim_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
lim_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
lim_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
lim                                                                   = "lim"
ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
ucw_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
ucw_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
ucw_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
ucw_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ucw                                                                   = "ucw"
unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
unfc_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
unfc_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
unfc_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
unfc_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
unfc                                                                   = "unfc"
ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
ueg_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
ueg_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
ueg_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
ueg_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ueg                                                                   = "ueg"
//ses
lsbfm_email_identity = "<EMAIL>"
ucw_email_identity   = "<EMAIL>"
ueg_email_identity    = "<EMAIL>"
unfc_email_identity   = "<EMAIL>"

ueg_agent_app_id = "dc5h4k76zh9j7"
ueg_student_app_id = "d2dj0g2r4k5xng"
lsbfmyr_agent_app_id = "d2tz7uodl4jv5k"
hzu_agent_app_id = "d1sc4nb1r5a337"
hzu_student_app_id = "d376xljl6vyvmm"
unfc_student_app_id = "d3y5bszbouuxz"
unfc_agent_app_id = "d4tp48kznihfb"
ucw_agent_app_id = "dv386lc6mkef4"
ucw_student_app_id = "ddbyr4biza5jy"
lim_agent_app_id = "d2x75o7bc5avd8"
lim_student_app_id = "d134molekv4w4"
ibat_agent_app_id = "d1aczfrvrm2uco"

//waf
api_gateway_arn = "arn:aws:apigateway:eu-west-1::/restapis/kjv7fhpkm7/stages/dev"

ard_agent_app_id = "d284l5yik16793"
wul_agent_app_id = "d22jxgdfggrdk2"
pu_agent_app_id =  "d3uk4t4ywvnlqr"
