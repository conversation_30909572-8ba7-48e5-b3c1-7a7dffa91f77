# Compiled files
*.tfstate
*.tfstate.backup

# Logs
logs
*.log

# Directories created by Terraform
.terraform

# Crash log files
crash.log

# Ignore all .tfvars files that are generated automatically for each Terraform run. Most
# .tfvars files are managed as part of configuration and so should be included in
# version control.
#
# example.tfvars
# *.tfvars

# Ignore override files as they can cause resource drift
override.tf
override.tf.json
*_override.tf
*_override.tf.json
.DS_Store

# Include override files you do wish to add to version control using negated pattern
#
# !example_override.tf

# Include tfplan files to ignore the plan output of command: terraform plan -out=tfplan
# example: *.tfplan

node_modules
