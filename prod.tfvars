region          = "eu-west-1"
environment     = "prod"
accountId       = "************"
environment_tag = "PROD"

//gateway
oap_gateway_certificate_acm_certificate_arn = "arn:aws:acm:us-east-1:************:certificate/c767ec40-a121-4998-9185-8493e7a45ad9"
oap_gateway_custom_domain                   = "oap-api.apphero.io"

//IAM
lambda_role_arn                                                 =  "arn:aws:iam::************:role/gus-lambda-exec-role-prod"
s3_cross_account_role_arn                                       =  "arn:aws:iam::************:role/s3OAPCrossAccountAccessRole-prod"
prod_oap_lambda_assumed_role_arn                                =  "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-prod/oap-backend-service-prod"
dev_oap_lambda_assumed_role_arn                                 =  "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-dev/oap-backend-service-dev"
dev_oap_lambda_notification_arn                                 =  "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-dev/oap-notifications-dev"
dev_eip_integration_lambda_arn                                  =  "arn:aws:sts::************:assumed-role/gus-eip-integration-handlers-access-dev/gus-sf-my-ucw-integration-dev"
prod_eip_integration_lambda_arn                                 =  "arn:aws:sts::************:assumed-role/gus-eip-integration-handlers-access-prod/gus-sf-my-ucw-integration-prod"
prod_oap_lambda_notification_arn                                =  "arn:aws:sts::************:assumed-role/oap-lambda-exec-role-prod/oap-notifications-prod"
prod_user_access_arn                                            = "arn:aws:iam::************:user/<EMAIL>"
dev_user_access_arn                                             = "arn:aws:iam::************:user/nishanth.g"
// code build
oap_frontend_project_source_type                             = "CODEPIPELINE"
oap_frontend_project_build_timeout                           = "5"
oap_frontend_project_environment_compute_type                = "BUILD_GENERAL1_SMALL"
oap_frontend_project_environment_image                       = "aws/codebuild/amazonlinux2-x86_64-standard:5.0"
oap_frontend_project_environment_type                        = "LINUX_CONTAINER"
oap_frontend_project_environment_image_pull_credentials_type = "CODEBUILD"
oap_frontend_project_artifact_type                           = "CODEPIPELINE"

// codepipeline
oap_frontend_pipeline_artifact_store_location       = "oap-frontend"
oap_frontend_pipeline_artifact_store_type           = "S3"
oap_frontend_pipeline_source_config_repository_name = "oap-frontend"
oap_frontend_pipeline_source_config_branch_name     = "prod"
oap_frontend_pipeline_project_name                  = "oap-frontend"

// cloudfront
s3_distribution_oap_frontend_domain_name                          = "oap-frontend-prod.s3-website-eu-west-1.amazonaws.com"
s3_distribution_oap_frontend_origin_id                            = "oap-frontend-prod.s3-website-eu-west-1.amazonaws.com"
s3_distribution_http_port                                         = 80
s3_distribution_https_port                                        = 443
s3_distribution_origin_protocol_policy                            = "http-only"
s3_distribution_origin_ssl_protocols                              = ["TLSv1.2"]
s3_distribution_enabled                                           = true
s3_distribution_is_ipv6_enabled                                   = true
s3_distribution_viewer_certificate_cloudfront_default_certificate = true
s3_distribution_default_cache_behavior_allowed_methods            = ["GET", "HEAD", "OPTIONS"]
s3_distribution_default_cache_behavior_cached_methods             = ["GET", "HEAD"]
s3_distribution_apphero_default_cache_behavior_target_origin_id   = "oap-frontend-prod.s3-website-eu-west-1.amazonaws.com"
s3_distribution_default_cache_behavior_cache_policy_id            = "658327ea-f89d-4fab-a63d-7e88639e58f6"
s3_distribution_default_cache_behavior_response_headers_policy_id = "eaab4381-ed33-4a86-88ca-d9558dc6cd63"
s3_distribution_viewer_protocol_policy                            = "redirect-to-https"
s3_distribution_geo_restriction_restriction_type                  = "none"
s3_distribution_viewer_certificate_acm_certificate_arn            = "arn:aws:acm:us-east-1:************:certificate/c767ec40-a121-4998-9185-8493e7a45ad9"

//sqs
canada_central_region                                             = "ca-central-1"
ireland_region                                                    = "eu-west-1"

//amplify
lim_oap_custom_domian_name         = "lim-oap"
ibat_oap_custom_domian_name        = "ibatel-oap"
hzu_oap_custom_domian_name         = "hzu-oap"
unfc_oap_custom_domian_name         = "unfc-oap"
ucw_student_oap_custom_domian_name = "apply"
hzu_student_oap_custom_domian_name = "hzu-student-oap"
ucw_agent_oap_custom_domian_name   = "ucw-oap"
lim_student_oap_custom_domian_name = "lim-student-oap"
unfc_student_oap_custom_domian_name = "unfc-student-oap"

//ecs lim oap
s3_role_arn="arn:aws:iam::************:role/s3CrossAccountAccessRole-prod"

//cognito
cognito_linkedin_identity_provider_client_id                          = "867qbkvwgfs1cb"
cognito_linkedin_identity_provider_client_secret                      = "Lqvn7ypbfXHopDkC"
cognito_linkedin_identity_provider_oidc_issuer                        = "https://www.linkedin.com/oauth"
cognito_linkedin_identity_provider_authorize_scopes                   = "email openid profile"
cognito_linkedin_identity_provider_attributes_request_method          = "GET"
hzu_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
hzu_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
hzu_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
hzu_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
hzu_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
hzu_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ses_mailer_arn                                                        = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>"
ucw_ses_mailer_arn                                                    = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>"
ueg_ses_mailer_arn                                                     = "arn:aws:ses:eu-west-1:************:identity/<EMAIL>" 
hzu                                                                   = "hzu"
user_type_student                                                     = "student"
lim_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
lim_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
lim_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
lim_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
lim_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
lim_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
lim                                                                   = "lim"
ucw_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
ucw_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
ucw_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
ucw_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
ucw_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
ucw_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ucw                                                                   = "ucw"
unfc_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
unfc_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
unfc_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
unfc_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
unfc_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
unfc_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
unfc                                                                   = "unfc"
ueg_student_oap_cognito_user_pool_client_allowed_oauth_flows          = ["code"]
ueg_student_oap_cognito_user_pool_client_allowed_oauth_scopes         = ["phone", "openid", "email", "profile", "aws.cognito.signin.user.admin"]
ueg_student_oap_cognito_user_pool_client_explicit_auth_flows          = ["ALLOW_USER_SRP_AUTH", "ALLOW_REFRESH_TOKEN_AUTH"]
ueg_student_oap_cognito_user_pool_client_callback_urls                = ["http://localhost:3000/my-application"]
ueg_student_oap_cognito_user_pool_client_logout_urls                  = ["http://localhost:3000/login"]
ueg_student_oap_cognito_user_pool_client_supported_identity_providers = ["COGNITO"]
ueg                                                                   = "ueg"

//ses
lsbfm_email_identity = "<EMAIL>"
ucw_email_identity   = "<EMAIL>"
ueg_email_identity    = "<EMAIL>"
unfc_email_identity   = "<EMAIL>"

ueg_agent_app_id = "d1tqoo02g9t3cs"
ueg_student_app_id = "d1q8cg7x5k6zwy"
lsbfmyr_agent_app_id = "d9z1bcde8vdge"
hzu_agent_app_id = "d1the8wwsvb7x5"
hzu_student_app_id = "d1t1llae28v7ob"
unfc_student_app_id = "d10fbmseq8br1f"
unfc_agent_app_id = "d18wxi7hh3iwyj"
ucw_agent_app_id = "dlwuyd4hfryhg"
ucw_student_app_id = "d15of6hqokt8z4"
lim_agent_app_id = "d2c6pdddzh6mlq"
lim_student_app_id = "d164sk7bxhsygt"
ibat_agent_app_id = "d2oaizoc5l0cdc"

//waf
api_gateway_arn = "arn:aws:apigateway:eu-west-1::/restapis/i34gvkc1ag/stages/prod"

ard_agent_app_id = "dc5h4k76zh9j7"
wul_agent_app_id = "dc5h4k76zh9j7"
pu_agent_app_id =  "d3uk4t4ywvnlqr"
